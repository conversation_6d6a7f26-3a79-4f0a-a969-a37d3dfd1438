# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 依赖目录
/node_modules
/server/node_modules
/client/node_modules

# 构建输出
/build
/client/build
/client/dist
/server/dist

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# 操作系统文件
.DS_Store
Thumbs.db

# IDE和编辑器文件
/.idea
/.vscode
*.swp
*.swo
.project
.classpath
.settings/

# 临时文件
*.tmp
*.temp
.cache/
/coverage

# Docker数据卷
/postgres-data
/pgadmin-data

# 其他
.vercel
.netlify
