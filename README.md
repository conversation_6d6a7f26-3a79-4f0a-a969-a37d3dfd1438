# AI 简历优化器

一个使用 AI 技术优化简历、提供面试准备和职位推荐的应用。

## 功能

- 简历优化
- 面试题推荐
- 面试经验分享
- 职位推荐
- 面试辅导

## 技术栈

- 前端：React, TypeScript, Material UI
- 后端：Node.js, Express
- 数据库：PostgreSQL (Docker)
- AI：DeepSeek API

## 数据库设计

系统包含三个主要数据表：

1. **面试题表 (interview_questions)**：存储各种面试题及其答案
2. **面经表 (interview_experiences)**：存储用户分享的面试经验
3. **推荐岗位表 (job_recommendations)**：存储职位推荐信息

## 开发环境设置

### 前提条件

- Node.js (v14+)
- Docker 和 Docker Compose
- Git

### 数据库设置 (使用 Docker)

1. 确保 Docker 和 Docker Compose 已安装

2. 启动数据库服务：

   ```bash
   ./start-db.sh
   ```

   如果需要同时启动 pgAdmin 管理工具：

   ```bash
   ./start-db.sh --with-pgadmin
   ```

3. 数据库连接信息：
   - 主机：localhost (或 Docker 容器中使用 'postgres')
   - 端口：5432
   - 用户名：postgres
   - 密码：postgres
   - 数据库名：ai_resume_optimizer

4. pgAdmin 访问信息 (如果已启动)：
   - URL：http://localhost:5050
   - 邮箱：<EMAIL>
   - 密码：admin

5. 停止数据库服务：

   ```bash
   docker-compose down
   ```

### 后端设置

1. 进入服务器目录：

   ```bash
   cd server
   ```

2. 安装依赖：

   ```bash
   npm install
   ```

3. 创建 .env 文件 (可以复制 .env.example)：

   ```bash
   cp .env.example .env
   ```

4. 启动服务器：

   ```bash
   npm start
   ```

5. 初始化数据库 (仅在开发环境)：

   ```bash
   curl -X POST http://localhost:3001/api/seed-database
   ```

### 前端设置

1. 进入客户端目录：

   ```bash
   cd client
   ```

2. 安装依赖：

   ```bash
   npm install
   ```

3. 启动开发服务器：

   ```bash
   npm run dev
   ```

## API 端点

### 面试题 API
- `GET /api/interview-questions` - 获取所有面试题
- `GET /api/interview-questions/:id` - 获取单个面试题
- `POST /api/interview-questions` - 创建面试题
- `PUT /api/interview-questions/:id` - 更新面试题
- `DELETE /api/interview-questions/:id` - 删除面试题
- `POST /api/interview-questions/:id/like` - 点赞面试题

### 面经 API
- `GET /api/interview-experiences` - 获取所有面经
- `GET /api/interview-experiences/:id` - 获取单个面经
- `POST /api/interview-experiences` - 创建面经
- `PUT /api/interview-experiences/:id` - 更新面经
- `DELETE /api/interview-experiences/:id` - 删除面经
- `POST /api/interview-experiences/:id/like` - 点赞面经

### 推荐岗位 API
- `GET /api/job-recommendations` - 获取所有推荐岗位
- `GET /api/job-recommendations/:id` - 获取单个推荐岗位
- `POST /api/job-recommendations` - 创建推荐岗位
- `PUT /api/job-recommendations/:id` - 更新推荐岗位
- `DELETE /api/job-recommendations/:id` - 删除推荐岗位
- `POST /api/job-recommendations/:id/apply` - 申请岗位
