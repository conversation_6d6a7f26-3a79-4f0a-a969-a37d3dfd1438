-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(30) NOT NULL UNIQUE,
  email VARCHAR(255) NOT NULL UNIQUE,
  "phoneNumber" VARCHAR(20) UNIQUE,
  "phoneVerified" BOOLEAN DEFAULT FALSE,
  password VARCHAR(255) NOT NULL,
  "fullName" VARCHAR(100),
  avatar VARCHAR(255),
  bio TEXT,
  role VARCHAR(10) CHECK (role IN ('user', 'admin')),
  "lastLogin" TIMESTAMP WITH TIME ZONE,
  "isActive" BOOLEAN DEFAULT TRUE,
  "resetPasswordToken" VARCHAR(255),
  "resetPasswordExpires" TIMESTAMP WITH TIME ZONE,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone_number ON users("phoneNumber");
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- 创建面试题表
CREATE TABLE IF NOT EXISTS interview_questions (
  id SERIAL PRIMARY KEY,
  question TEXT NOT NULL,
  answer TEXT,
  difficulty VARCHAR(10) CHECK (difficulty IN ('简单', '中等', '困难')),
  category VARCHAR(50) NOT NULL,
  tags TEXT[] DEFAULT '{}',
  company VARCHAR(100),
  position VARCHAR(100),
  views INTEGER DEFAULT 0,
  likes INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建面试题表索引
CREATE INDEX IF NOT EXISTS idx_interview_questions_category ON interview_questions(category);
CREATE INDEX IF NOT EXISTS idx_interview_questions_difficulty ON interview_questions(difficulty);
CREATE INDEX IF NOT EXISTS idx_interview_questions_company ON interview_questions(company);
CREATE INDEX IF NOT EXISTS idx_interview_questions_position ON interview_questions(position);

-- 创建面经表
CREATE TABLE IF NOT EXISTS interview_experiences (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  company VARCHAR(100) NOT NULL,
  position VARCHAR(100) NOT NULL,
  date TIMESTAMP WITH TIME ZONE NOT NULL,
  content TEXT NOT NULL,
  rounds INTEGER,
  result VARCHAR(10) CHECK (result IN ('通过', '未通过', '等待中', '未知')),
  difficulty VARCHAR(10) CHECK (difficulty IN ('简单', '中等', '困难')),
  duration VARCHAR(50),
  tags TEXT[] DEFAULT '{}',
  author VARCHAR(100),
  "authorId" INTEGER,
  views INTEGER DEFAULT 0,
  likes INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建面经表索引
CREATE INDEX IF NOT EXISTS idx_interview_experiences_company ON interview_experiences(company);
CREATE INDEX IF NOT EXISTS idx_interview_experiences_position ON interview_experiences(position);
CREATE INDEX IF NOT EXISTS idx_interview_experiences_date ON interview_experiences(date);
CREATE INDEX IF NOT EXISTS idx_interview_experiences_result ON interview_experiences(result);

-- 创建推荐岗位表
CREATE TABLE IF NOT EXISTS job_recommendations (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  company VARCHAR(100) NOT NULL,
  location VARCHAR(100) NOT NULL,
  salary VARCHAR(50),
  description TEXT NOT NULL,
  requirements TEXT,
  "jobType" VARCHAR(20) CHECK ("jobType" IN ('全职', '兼职', '实习', '合同', '自由职业')),
  "experienceLevel" VARCHAR(20) CHECK ("experienceLevel" IN ('入门级', '初级', '中级', '高级', '专家级')),
  "educationLevel" VARCHAR(20) CHECK ("educationLevel" IN ('高中', '大专', '本科', '硕士', '博士', '不限')),
  skills TEXT[] DEFAULT '{}',
  benefits TEXT[] DEFAULT '{}',
  "applicationUrl" VARCHAR(255),
  "companyLogo" VARCHAR(255),
  featured BOOLEAN DEFAULT FALSE,
  "postedDate" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "expiryDate" TIMESTAMP WITH TIME ZONE,
  views INTEGER DEFAULT 0,
  applications INTEGER DEFAULT 0,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建推荐岗位表索引
CREATE INDEX IF NOT EXISTS idx_job_recommendations_title ON job_recommendations(title);
CREATE INDEX IF NOT EXISTS idx_job_recommendations_company ON job_recommendations(company);
CREATE INDEX IF NOT EXISTS idx_job_recommendations_location ON job_recommendations(location);
CREATE INDEX IF NOT EXISTS idx_job_recommendations_job_type ON job_recommendations("jobType");
CREATE INDEX IF NOT EXISTS idx_job_recommendations_featured ON job_recommendations(featured);
CREATE INDEX IF NOT EXISTS idx_job_recommendations_posted_date ON job_recommendations("postedDate");
