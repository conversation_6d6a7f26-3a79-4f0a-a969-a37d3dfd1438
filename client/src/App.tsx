import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import ResumeOptimizer from './components/ResumeOptimizer';
import InterviewExperiences from './components/InterviewExperiences';
import JobRecommendations from './components/JobRecommendations';
import InterviewCoaching from './components/InterviewCoaching';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import UserProfile from './components/auth/UserProfile';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Navigation from './components/Navigation';
import { Box, Container, Snackbar, Alert } from '@mui/material';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00b4d8',
      light: '#48cae4',
      dark: '#0096c7',
    },
    secondary: {
      main: '#90e0ef',
      light: '#ade8f4',
      dark: '#48cae4',
    },
    background: {
      default: '#0a192f',
      paper: '#112240',
    },
    text: {
      primary: '#e6f1ff',
      secondary: '#8892b0',
    },
    error: {
      main: '#ff5d8f',
    },
    success: {
      main: '#4ecca3',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 800,
      fontSize: '3.5rem',
      background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      letterSpacing: '-0.02em',
      marginBottom: '0.5em',
      textShadow: '0 0 40px rgba(0, 180, 216, 0.3)',
    },
    h2: {
      fontWeight: 700,
      fontSize: '2.5rem',
      letterSpacing: '-0.01em',
    },
    h3: {
      fontWeight: 600,
      fontSize: '2rem',
      letterSpacing: '-0.01em',
    },
    h4: {
      fontWeight: 600,
      letterSpacing: '-0.01em',
    },
    button: {
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        '*::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '*::-webkit-scrollbar-thumb': {
          backgroundColor: 'rgba(0, 180, 216, 0.3)',
          borderRadius: '4px',
        },
        '*::-webkit-scrollbar-track': {
          backgroundColor: 'rgba(0, 0, 0, 0.1)',
        },
        body: {
          scrollBehavior: 'smooth',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 10,
          textTransform: 'none',
          fontWeight: 600,
          padding: '10px 24px',
          transition: 'all 0.3s ease',
          position: 'relative',
          overflow: 'hidden',
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: '-100%',
            width: '100%',
            height: '100%',
            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
            transition: 'all 0.5s ease',
          },
          '&:hover::after': {
            left: '100%',
          },
        },
        contained: {
          background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
          boxShadow: '0 4px 20px rgba(0, 180, 216, 0.4)',
          '&:hover': {
            background: 'linear-gradient(45deg, #0096c7 30%, #48cae4 90%)',
            transform: 'translateY(-2px)',
            boxShadow: '0 6px 25px rgba(0, 180, 216, 0.5)',
          },
        },
        outlined: {
          borderWidth: '2px',
          '&:hover': {
            borderWidth: '2px',
            background: 'rgba(0, 180, 216, 0.05)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          background: 'rgba(17, 34, 64, 0.7)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
          transition: 'all 0.3s ease',
          overflow: 'hidden',
          '&:hover': {
            boxShadow: '0 15px 40px rgba(0, 0, 0, 0.3)',
            transform: 'translateY(-5px)',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            transition: 'all 0.3s ease',
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: '#00b4d8',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderWidth: '2px',
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
          },
        },
      },
    },
    MuiAccordion: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          '&:before': {
            display: 'none',
          },
          '&.Mui-expanded': {
            margin: '16px 0',
          },
        },
      },
    },
    MuiAccordionSummary: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});

// 创建背景图案
const BackgroundPattern = () => (
  <Box
    sx={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: -1,
      opacity: 0.4,
      pointerEvents: 'none',
      background: `
        radial-gradient(circle at 25% 25%, rgba(0, 180, 216, 0.05) 1%, transparent 8%),
        radial-gradient(circle at 75% 44%, rgba(144, 224, 239, 0.05) 1%, transparent 6%),
        radial-gradient(circle at 46% 67%, rgba(0, 180, 216, 0.05) 1%, transparent 8%),
        radial-gradient(circle at 83% 86%, rgba(144, 224, 239, 0.05) 1%, transparent 6%),
        radial-gradient(circle at 15% 95%, rgba(0, 180, 216, 0.05) 1%, transparent 6%)
      `,
      backgroundSize: '120% 120%',
    }}
  />
);

// 登录重定向组件
const LoginRedirect = () => {
  const location = useLocation();
  return <Navigate to="/login" state={{ from: location.pathname }} replace />;
};

function App() {
  // 用户认证状态
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authenticationChecked, setAuthenticationChecked] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [token, setToken] = useState<string>('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // 检查本地存储中的认证信息
  useEffect(() => {
    const checkAuthentication = async () => {
      const storedToken = localStorage.getItem('token');
      const storedUser = localStorage.getItem('user');

      if (storedToken && storedUser) {
        try {
          // 验证令牌有效性
          const response = await fetch('http://localhost:3001/api/auth/me', {
            headers: {
              'Authorization': `Bearer ${storedToken}`
            }
          });

          if (response.ok) {
            // 令牌有效
            setToken(storedToken);
            setUser(JSON.parse(storedUser));
            setIsAuthenticated(true);
          } else {
            // 令牌无效，清除本地存储
            localStorage.removeItem('token');
            localStorage.removeItem('user');
          }
        } catch (error) {
          console.error('验证令牌失败:', error);
        }
      }

      // 无论结果如何，都标记认证检查已完成
      setAuthenticationChecked(true);
    };

    checkAuthentication();
  }, []);

  // 处理登录成功
  const handleLoginSuccess = (userData: any, token: string) => {
    setUser(userData);
    setToken(token);
    setIsAuthenticated(true);
    setNotification({
      open: true,
      message: `欢迎回来，${userData.fullName || userData.username}！`,
      severity: 'success'
    });
  };

  // 处理注册成功
  const handleRegisterSuccess = (userData: any, token: string) => {
    setUser(userData);
    setToken(token);
    setIsAuthenticated(true);
    setNotification({
      open: true,
      message: '注册成功！欢迎加入我们！',
      severity: 'success'
    });
  };

  // 处理登出
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    setToken('');
    setIsAuthenticated(false);
    setNotification({
      open: true,
      message: '您已成功登出',
      severity: 'success'
    });
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box
          sx={{
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #0a192f 0%, #112240 100%)',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <BackgroundPattern />
          <Navigation isAuthenticated={isAuthenticated} user={user} onLogout={handleLogout} />
          <Container
            maxWidth="xl"
            sx={{
              flexGrow: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              pt: { xs: 10, md: 12 }, // 增加顶部间距，并根据屏幕大小调整
              pb: { xs: 6, md: 8 },
              px: { xs: 2, sm: 3, md: 4 }, // 响应式内边距
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: '5%',
                right: '-15%',
                width: '500px',
                height: '500px',
                background: 'radial-gradient(circle, rgba(0, 180, 216, 0.1) 0%, transparent 70%)',
                borderRadius: '50%',
                filter: 'blur(60px)',
                zIndex: -1,
                animation: 'float 15s ease-in-out infinite alternate',
              },
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: '5%',
                left: '-10%',
                width: '400px',
                height: '400px',
                background: 'radial-gradient(circle, rgba(144, 224, 239, 0.1) 0%, transparent 70%)',
                borderRadius: '50%',
                filter: 'blur(60px)',
                zIndex: -1,
                animation: 'float 20s ease-in-out infinite alternate-reverse',
              },
              '@keyframes float': {
                '0%': { transform: 'translateY(0) scale(1)' },
                '50%': { transform: 'translateY(-30px) scale(1.05)' },
                '100%': { transform: 'translateY(20px) scale(0.95)' },
              },
            }}
          >
            <Routes>
              <Route
                path="/"
                element={
                  <ProtectedRoute
                    isAuthenticated={isAuthenticated}
                    authenticationChecked={authenticationChecked}
                  >
                    <ResumeOptimizer />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/interview-experiences"
                element={
                  <ProtectedRoute
                    isAuthenticated={isAuthenticated}
                    authenticationChecked={authenticationChecked}
                  >
                    <InterviewExperiences />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/job-recommendations"
                element={
                  <ProtectedRoute
                    isAuthenticated={isAuthenticated}
                    authenticationChecked={authenticationChecked}
                  >
                    <JobRecommendations />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/interview-coaching"
                element={
                  <ProtectedRoute
                    isAuthenticated={isAuthenticated}
                    authenticationChecked={authenticationChecked}
                  >
                    <InterviewCoaching />
                  </ProtectedRoute>
                }
              />
              <Route path="/login" element={<Login onLoginSuccess={handleLoginSuccess} />} />
              <Route path="/register" element={<Register onRegisterSuccess={handleRegisterSuccess} />} />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute
                    isAuthenticated={isAuthenticated}
                    authenticationChecked={authenticationChecked}
                  >
                    <UserProfile token={token} onLogout={handleLogout} />
                  </ProtectedRoute>
                }
              />
            </Routes>
          </Container>

          {/* 通知提示 */}
          <Snackbar
            open={notification.open}
            autoHideDuration={5000}
            onClose={handleCloseNotification}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert
              onClose={handleCloseNotification}
              severity={notification.severity}
              variant="filled"
              sx={{ width: '100%' }}
            >
              {notification.message}
            </Alert>
          </Snackbar>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
