import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Fade,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Skeleton,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  ContentCopy as ContentCopyIcon,
  Check as CheckIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  LightbulbOutlined as LightbulbIcon,
  QuestionAnswer as QuestionAnswerIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  ArrowForward as ArrowForwardIcon,
  Rocket as RocketIcon,
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { zhTranslations } from '../translations/zh';

interface OptimizationResult {
  summary: string;
  sections: {
    title: string;
    suggestions: string[];
  }[];
  improvedVersion: string;
}

const ResumeOptimizer = () => {
  const [optimizationResult, setOptimizationResult] = useState<OptimizationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [fileName, setFileName] = useState('');
  const [copied, setCopied] = useState(false);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileName(file.name);
      setError('');
    }
  };

  const handleOptimize = async () => {
    if (!fileName) {
      setError(zhTranslations.resumeOptimizer.noFileSelected);
      return;
    }

    setLoading(true);
    setError('');
    setOptimizationResult(null);

    try {
      // 从localStorage获取认证token
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未登录，请先登录');
      }

      const formData = new FormData();
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      if (fileInput?.files?.[0]) {
        formData.append('resume', fileInput.files[0]);
      } else {
        throw new Error(zhTranslations.resumeOptimizer.noFileSelected);
      }

      const response = await fetch('http://localhost:3001/api/optimize-resume', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData, // 不要设置 Content-Type，让浏览器自动设置正确的 boundary
      });

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('认证失败，请重新登录');
        }
        throw new Error(zhTranslations.resumeOptimizer.failedToOptimize);
      }

      const data = await response.json();
      setOptimizationResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : zhTranslations.resumeOptimizer.anErrorOccurred);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = () => {
    if (optimizationResult) {
      navigator.clipboard.writeText(optimizationResult.improvedVersion);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleClear = () => {
    setFileName('');
    setOptimizationResult(null);
    setError('');
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  const renderLoadingState = () => (
    <Card sx={{
      height: '100%',
      p: { xs: 3, md: 4 },
      textAlign: 'center',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '400px',
      background: 'rgba(17, 34, 64, 0.5)',
    }}>
      <Box sx={{ position: 'relative' }}>
        <CircularProgress
          size={80}
          thickness={4}
          sx={{
            color: 'primary.main',
            animation: 'pulse 1.5s ease-in-out infinite',
            '@keyframes pulse': {
              '0%': { opacity: 0.6, transform: 'scale(0.95)' },
              '50%': { opacity: 1, transform: 'scale(1.05)' },
              '100%': { opacity: 0.6, transform: 'scale(0.95)' },
            },
          }}
        />
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <CircularProgress
            size={80}
            thickness={4}
            sx={{
              color: 'secondary.main',
              opacity: 0.4,
              position: 'absolute',
              animation: 'spin 3s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' },
              },
            }}
          />
        </Box>
      </Box>
      <Typography
        variant="h6"
        sx={{
          mt: 4,
          mb: 3,
          color: 'primary.light',
          fontWeight: 600,
          animation: 'fadeInOut 2s ease-in-out infinite',
          '@keyframes fadeInOut': {
            '0%': { opacity: 0.7 },
            '50%': { opacity: 1 },
            '100%': { opacity: 0.7 },
          },
        }}
      >
        {zhTranslations.resumeOptimizer.analyzing}
      </Typography>
      <Box sx={{ mt: 2, width: '100%', maxWidth: '500px', mx: 'auto' }}>
        <Skeleton variant="text" sx={{ mb: 2, height: 20, borderRadius: 1 }} />
        <Skeleton variant="text" sx={{ mb: 2, height: 20, borderRadius: 1 }} />
        <Skeleton variant="text" sx={{ mb: 2, height: 20, width: '80%', borderRadius: 1 }} />
        <Skeleton variant="rectangular" sx={{ mt: 4, height: 100, borderRadius: 2 }} />
      </Box>
    </Card>
  );

  const renderResults = () => (
    <>
      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            animation: 'fadeIn 0.5s ease-in-out',
            '@keyframes fadeIn': {
              '0%': { opacity: 0, transform: 'translateY(-10px)' },
              '100%': { opacity: 1, transform: 'translateY(0)' },
            },
          }}
        >
          {error}
        </Alert>
      )}
      {optimizationResult && (
        <Box sx={{ mt: 4 }}>
          <Alert
            severity="info"
            variant="filled"
            icon={<LightbulbIcon />}
            sx={{
              mb: 4,
              p: 2,
              borderRadius: 2,
              background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.15) 0%, rgba(144, 224, 239, 0.1) 100%)',
              border: '1px solid rgba(0, 180, 216, 0.2)',
              color: 'white',
              '& .MuiAlert-icon': {
                color: 'primary.light',
                opacity: 1,
              },
              animation: 'fadeIn 0.8s ease-in-out',
              '@keyframes fadeIn': {
                '0%': { opacity: 0, transform: 'translateY(-10px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            <Typography sx={{ fontWeight: 500, fontSize: '1rem' }}>
              {optimizationResult.summary}
            </Typography>
          </Alert>

          {optimizationResult.sections.map((section, index) => (
            <Accordion
              key={index}
              sx={{
                mb: 2,
                background: 'rgba(17, 34, 64, 0.5)',
                backdropFilter: 'blur(5px)',
                borderRadius: '12px !important',
                border: '1px solid rgba(255, 255, 255, 0.05)',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 8px 20px rgba(0, 0, 0, 0.15)',
                  transform: 'translateY(-2px)',
                },
                '&.Mui-expanded': {
                  margin: '12px 0',
                  boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
                },
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon sx={{ color: 'primary.main' }} />}
                sx={{
                  borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
                  '& .MuiAccordionSummary-content': {
                    margin: '12px 0',
                  },
                  '&.Mui-expanded': {
                    background: 'rgba(0, 180, 216, 0.05)',
                  },
                  '&:hover': {
                    background: 'rgba(0, 180, 216, 0.05)',
                  },
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    color: 'primary.light',
                    fontWeight: 600,
                    fontSize: { xs: '1.1rem', md: '1.2rem' },
                  }}
                >
                  {section.title}
                </Typography>
              </AccordionSummary>
              <AccordionDetails sx={{ p: { xs: 2, md: 3 } }}>
                <List sx={{ p: 0 }}>
                  {section.suggestions.map((suggestion, idx) => (
                    <ListItem
                      key={idx}
                      sx={{
                        px: 2,
                        py: 1.5,
                        borderRadius: 2,
                        mb: 1,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          background: 'rgba(0, 180, 216, 0.05)',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        <LightbulbIcon color="primary" fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography sx={{ fontSize: '0.95rem', lineHeight: 1.5 }}>
                            {suggestion}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </AccordionDetails>
            </Accordion>
          ))}

          <Card
            sx={{
              mt: 4,
              position: 'relative',
              overflow: 'visible',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: '-5px',
                left: '-5px',
                right: '-5px',
                bottom: '-5px',
                background: 'linear-gradient(135deg, rgba(144, 224, 239, 0.2) 0%, rgba(0, 180, 216, 0.1) 100%)',
                borderRadius: '20px',
                filter: 'blur(15px)',
                opacity: 0.7,
                zIndex: -1,
              },
            }}
          >
            <CardContent sx={{ p: { xs: 3, md: 4 } }}>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3,
                pb: 2,
                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
              }}>
                <Typography
                  variant="h5"
                  sx={{
                    color: 'primary.light',
                    fontWeight: 600,
                    fontSize: { xs: '1.4rem', md: '1.6rem' },
                  }}
                >
                  {zhTranslations.resumeOptimizer.optimizedVersion}
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={copied ? <CheckIcon /> : <ContentCopyIcon />}
                  onClick={handleCopy}
                  color={copied ? "success" : "primary"}
                  sx={{
                    minWidth: 'auto',
                    transition: 'all 0.3s ease',
                  }}
                >
                  {copied ? '已复制' : '复制'}
                </Button>
              </Box>
              <Box
                sx={{
                  whiteSpace: 'pre-wrap',
                  p: 3,
                  bgcolor: 'rgba(17, 34, 64, 0.6)',
                  borderRadius: 2,
                  border: '1px solid rgba(255, 255, 255, 0.05)',
                  maxHeight: '500px',
                  overflow: 'auto',
                  fontFamily: 'monospace',
                  fontSize: '0.95rem',
                  lineHeight: 1.7,
                  position: 'relative',
                  '&::-webkit-scrollbar': {
                    width: '8px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: 'rgba(0, 180, 216, 0.3)',
                    borderRadius: '4px',
                  },
                }}
              >
                {optimizationResult.improvedVersion}
              </Box>
            </CardContent>
          </Card>

          {/* 下一步行动建议卡片 */}
          <Card
            sx={{
              mt: 4,
              mb: 2,
              position: 'relative',
              overflow: 'visible',
              background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.1) 0%, rgba(17, 34, 64, 0.8) 100%)',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: '-5px',
                left: '-5px',
                right: '-5px',
                bottom: '-5px',
                background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.2) 0%, rgba(144, 224, 239, 0.1) 100%)',
                borderRadius: '20px',
                filter: 'blur(15px)',
                opacity: 0.7,
                zIndex: -1,
              },
              animation: 'fadeIn 0.8s ease-in-out',
              '@keyframes fadeIn': {
                '0%': { opacity: 0, transform: 'translateY(20px)' },
                '100%': { opacity: 1, transform: 'translateY(0)' },
              },
            }}
          >
            <CardContent sx={{ p: { xs: 3, md: 4 } }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 3,
              }}>
                <RocketIcon sx={{
                  color: 'primary.light',
                  mr: 1.5,
                  fontSize: { xs: '1.8rem', md: '2rem' },
                  animation: 'float 3s ease-in-out infinite alternate',
                  '@keyframes float': {
                    '0%': { transform: 'translateY(0)' },
                    '100%': { transform: 'translateY(-5px)' },
                  },
                }} />
                <Typography
                  variant="h5"
                  sx={{
                    color: 'primary.light',
                    fontWeight: 600,
                    fontSize: { xs: '1.4rem', md: '1.6rem' },
                    textAlign: 'center',
                  }}
                >
                  准备面试的下一步行动
                </Typography>
              </Box>

              <Box sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' },
                gap: 3,
                mt: 2,
              }}>
                {/* 推荐面试题卡片 */}
                <Card sx={{
                  background: 'rgba(17, 34, 64, 0.6)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)',
                  },
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}>
                  <CardContent sx={{
                    p: 3,
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <QuestionAnswerIcon sx={{ color: 'primary.light', mr: 1 }} />
                      <Typography variant="h6" sx={{ color: 'primary.light' }}>
                        推荐面试题
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ mb: 3, flexGrow: 1 }}>
                      根据您的简历，我们为您精选了相关的面试题，帮助您更好地准备技术面试。
                    </Typography>
                    <Button
                      variant="contained"
                      component={RouterLink}
                      to="/interview-experiences"
                      fullWidth
                      endIcon={<ArrowForwardIcon />}
                      sx={{
                        mt: 'auto',
                        background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                      }}
                    >
                      查看推荐面试题
                    </Button>
                  </CardContent>
                </Card>

                {/* 面试辅导卡片 */}
                <Card sx={{
                  background: 'rgba(17, 34, 64, 0.6)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)',
                  },
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}>
                  <CardContent sx={{
                    p: 3,
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PersonIcon sx={{ color: 'primary.light', mr: 1 }} />
                      <Typography variant="h6" sx={{ color: 'primary.light' }}>
                        面试辅导
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ mb: 3, flexGrow: 1 }}>
                      与行业专家一对一辅导，获取针对您简历和目标职位的个性化面试建议。
                    </Typography>
                    <Button
                      variant="contained"
                      component={RouterLink}
                      to="/interview-coaching"
                      fullWidth
                      endIcon={<ArrowForwardIcon />}
                      sx={{
                        mt: 'auto',
                        background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                      }}
                    >
                      预约面试辅导
                    </Button>
                  </CardContent>
                </Card>

                {/* 职位推荐卡片 */}
                <Card sx={{
                  background: 'rgba(17, 34, 64, 0.6)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 10px 20px rgba(0, 0, 0, 0.2)',
                  },
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}>
                  <CardContent sx={{
                    p: 3,
                    flexGrow: 1,
                    display: 'flex',
                    flexDirection: 'column',
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <WorkIcon sx={{ color: 'primary.light', mr: 1 }} />
                      <Typography variant="h6" sx={{ color: 'primary.light' }}>
                        职位推荐
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ mb: 3, flexGrow: 1 }}>
                      基于您的简历和技能，我们为您匹配了最适合的职位，提高求职效率。
                    </Typography>
                    <Button
                      variant="contained"
                      component={RouterLink}
                      to="/job-recommendations"
                      fullWidth
                      endIcon={<ArrowForwardIcon />}
                      sx={{
                        mt: 'auto',
                        background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                      }}
                    >
                      查看职位推荐
                    </Button>
                  </CardContent>
                </Card>
              </Box>
            </CardContent>
          </Card>
        </Box>
      )}
    </>
  );

  return (
    <Box sx={{ width: '100%', mt: { xs: 2, md: 4 } }}>
      <Fade in={true} timeout={800}>
        <Box>
          <Typography
            variant="h1"
            align="center"
            gutterBottom
            sx={{
              fontSize: { xs: '2.5rem', sm: '3rem', md: '3.5rem' },
              px: 2,
              animation: 'glow 3s ease-in-out infinite alternate',
              '@keyframes glow': {
                '0%': { textShadow: '0 0 20px rgba(0, 180, 216, 0.3)' },
                '100%': { textShadow: '0 0 40px rgba(0, 180, 216, 0.6)' },
              },
            }}
          >
            {zhTranslations.resumeOptimizer.title}
          </Typography>
          <Typography
            variant="h3"
            align="center"
            sx={{
              mb: { xs: 4, md: 6 },
              color: 'text.secondary',
              fontSize: { xs: '1.4rem', sm: '1.6rem', md: '2rem' },
              maxWidth: '800px',
              mx: 'auto',
              px: 2,
            }}
          >
            {zhTranslations.resumeOptimizer.subtitle}
          </Typography>
        </Box>
      </Fade>

      <Box sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Box
          sx={{
            display: { xs: 'flex', md: 'grid' },
            flexDirection: { xs: 'column', md: 'unset' },
            gridTemplateColumns: { md: '5fr 7fr' },
            gap: { xs: 3, md: 5 },
            width: '100%',
            alignItems: 'flex-start', // 确保项目从顶部对齐
          }}
        >
          {/* 左侧上传区域 */}
          <Fade in={true} timeout={1000} style={{ transitionDelay: '200ms' }}>
            <Card
              sx={{
                width: { xs: '100%', md: 'auto' },
                position: 'sticky', // 使卡片在滚动时保持可见
                overflow: 'visible',
                alignSelf: 'flex-start', // 确保卡片不会被拉伸
                top: { md: '100px' }, // 滚动时的顶部距离
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: '-10px',
                  left: '-10px',
                  right: '-10px',
                  bottom: '-10px',
                  background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.2) 0%, rgba(144, 224, 239, 0.1) 100%)',
                  borderRadius: '20px',
                  filter: 'blur(20px)',
                  opacity: 0.7,
                  zIndex: -1,
                },
              }}
            >
              <CardContent sx={{ p: { xs: 3, md: 4 } }}>
                <Typography
                  variant="h2"
                  gutterBottom
                  sx={{
                    fontSize: { xs: '1.8rem', md: '2.2rem' },
                    mb: 3,
                    color: 'primary.light',
                    textAlign: { xs: 'center', md: 'left' },
                  }}
                >
                  {zhTranslations.resumeOptimizer.uploadResume}
                </Typography>
                <Box
                  sx={{
                    border: '2px dashed',
                    borderColor: 'primary.main',
                    borderRadius: 3,
                    p: { xs: 4, md: 6 },
                    textAlign: 'center',
                    mb: 3,
                    height: { xs: 'auto', md: '280px' }, // 固定高度，防止被拉伸
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: 2,
                    background: 'rgba(0, 180, 216, 0.03)',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      borderColor: 'primary.light',
                      background: 'rgba(0, 180, 216, 0.05)',
                      transform: 'scale(1.02)',
                    }
                  }}
                >
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                    style={{ display: 'none' }}
                    id="file-upload"
                  />
                  <label htmlFor="file-upload">
                    <Button
                      component="span"
                      variant="contained"
                      startIcon={<CloudUploadIcon />}
                      size="large"
                      sx={{
                        mb: 2,
                        px: 3,
                        py: 1.5,
                        fontSize: '1.1rem',
                      }}
                    >
                      {zhTranslations.resumeOptimizer.chooseFile}
                    </Button>
                  </label>
                  {fileName && (
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      sx={{
                        p: 1,
                        borderRadius: 1,
                        background: 'rgba(255, 255, 255, 0.05)',
                        maxWidth: '100%',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {zhTranslations.resumeOptimizer.selected} {fileName}
                    </Typography>
                  )}
                </Box>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                  <Button
                    variant="contained"
                    onClick={handleOptimize}
                    disabled={!fileName || loading}
                    size="large"
                    sx={{
                      px: 4,
                      py: 1.2,
                      flexGrow: 1,
                      maxWidth: '200px',
                    }}
                  >
                    {loading ? <CircularProgress size={24} /> : zhTranslations.resumeOptimizer.optimizeResume}
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleClear}
                    startIcon={<ClearIcon />}
                    size="large"
                    sx={{
                      px: 3,
                      py: 1.2,
                    }}
                  >
                    {zhTranslations.resumeOptimizer.clear}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Fade>

          {/* 右侧结果区域 */}
          <Fade in={true} timeout={1000} style={{ transitionDelay: '400ms' }}>
            <Box sx={{ width: '100%' }}>
              {loading ? renderLoadingState() : renderResults()}
            </Box>
          </Fade>
        </Box>
      </Box>
    </Box>
  );
};

export default ResumeOptimizer;
