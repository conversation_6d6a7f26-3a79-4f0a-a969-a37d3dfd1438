import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material';
import {
  Videocam as VideocamIcon,
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { zhTranslations } from '../translations/zh';

// Mock data for coaches
const mockCoaches = [
  {
    id: 1,
    name: zhTranslations.interviewCoaching.mockData.names[0],
    title: zhTranslations.interviewCoaching.mockData.titles[0],
    company: zhTranslations.interviewCoaching.mockData.companies[0],
    experience: zhTranslations.interviewCoaching.mockData.experiences[0],
    specialties: zhTranslations.interviewCoaching.mockData.specialties[0],
    rating: 4.9,
    reviews: 127,
    imageUrl: 'https://randomuser.me/api/portraits/women/44.jpg',
    availability: zhTranslations.interviewCoaching.mockData.availabilities[0]
  },
  {
    id: 2,
    name: zhTranslations.interviewCoaching.mockData.names[1],
    title: zhTranslations.interviewCoaching.mockData.titles[1],
    company: zhTranslations.interviewCoaching.mockData.companies[1],
    experience: zhTranslations.interviewCoaching.mockData.experiences[1],
    specialties: zhTranslations.interviewCoaching.mockData.specialties[1],
    rating: 4.8,
    reviews: 93,
    imageUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
    availability: zhTranslations.interviewCoaching.mockData.availabilities[1]
  },
  {
    id: 3,
    name: zhTranslations.interviewCoaching.mockData.names[2],
    title: zhTranslations.interviewCoaching.mockData.titles[2],
    company: zhTranslations.interviewCoaching.mockData.companies[2],
    experience: zhTranslations.interviewCoaching.mockData.experiences[2],
    specialties: zhTranslations.interviewCoaching.mockData.specialties[2],
    rating: 4.7,
    reviews: 85,
    imageUrl: 'https://randomuser.me/api/portraits/women/66.jpg',
    availability: zhTranslations.interviewCoaching.mockData.availabilities[2]
  }
];

// Benefits of coaching
const coachingBenefits = zhTranslations.interviewCoaching.benefits;

const InterviewCoaching = () => {
  return (
    <Box sx={{ maxWidth: 1200, width: '100%', px: 3, mt: 4 }}>
      <Typography variant="h1" align="center" gutterBottom>
        {zhTranslations.interviewCoaching.title}
      </Typography>
      <Typography variant="h3" align="center" sx={{ mb: 6, color: 'text.secondary' }}>
        {zhTranslations.interviewCoaching.subtitle}
      </Typography>

      {/* Benefits Section */}
      <Card sx={{ mb: 6 }}>
        <CardContent>
          <Typography variant="h4" gutterBottom align="center">
            {zhTranslations.interviewCoaching.whyChoose}
          </Typography>
          <Grid container spacing={3} sx={{ mt: 2 }}>
            {coachingBenefits.map((benefit, index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <CheckCircleIcon sx={{ color: 'primary.main', mr: 2, mt: 0.5 }} />
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      {benefit.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {benefit.description}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Coaches Section */}
      <Typography variant="h4" gutterBottom>
        {zhTranslations.interviewCoaching.ourExpertCoaches}
      </Typography>
      <Grid container spacing={3}>
        {mockCoaches.map((coach) => (
          <Grid item xs={12} md={4} key={coach.id}>
            <Card sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 12px 20px rgba(0, 0, 0, 0.2)',
              }
            }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    src={coach.imageUrl}
                    alt={coach.name}
                    sx={{ width: 100, height: 100, mb: 2 }}
                  />
                  <Typography variant="h5" align="center">
                    {coach.name}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary" align="center" gutterBottom>
                    {coach.title} at {coach.company}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <StarIcon sx={{ color: '#FFD700', mr: 0.5 }} fontSize="small" />
                    <Typography variant="body2">
                      {coach.rating} ({coach.reviews} {zhTranslations.interviewCoaching.reviews})
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {coach.experience} {zhTranslations.interviewCoaching.experience}
                  </Typography>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                  {zhTranslations.interviewCoaching.specialties}
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {coach.specialties.map((specialty) => (
                    <Chip
                      key={specialty}
                      label={specialty}
                      size="small"
                      sx={{
                        background: 'linear-gradient(45deg, rgba(0, 180, 216, 0.2) 30%, rgba(144, 224, 239, 0.2) 90%)',
                        border: '1px solid rgba(0, 180, 216, 0.3)',
                      }}
                    />
                  ))}
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <ScheduleIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2" color="text.secondary">
                    {coach.availability}
                  </Typography>
                </Box>

                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<VideocamIcon />}
                >
                  {zhTranslations.interviewCoaching.bookSession}
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Call to Action */}
      <Card sx={{ mt: 6, background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.2) 0%, rgba(144, 224, 239, 0.2) 100%)' }}>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h4" gutterBottom>
            {zhTranslations.interviewCoaching.readyToAce}
          </Typography>
          <Typography variant="body1" sx={{ mb: 3, maxWidth: 700, mx: 'auto' }}>
            {zhTranslations.interviewCoaching.coachesHelpedThousands}
          </Typography>
          <Button
            variant="contained"
            size="large"
            endIcon={<ArrowForwardIcon />}
            sx={{
              px: 4,
              py: 1.5,
              fontSize: '1.1rem',
              boxShadow: '0 4px 20px rgba(0, 180, 216, 0.4)',
            }}
          >
            {zhTranslations.interviewCoaching.browseAllCoaches}
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
};

export default InterviewCoaching;
