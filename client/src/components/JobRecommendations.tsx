import { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Divider,
  Rating
} from '@mui/material';
import {
  Work as WorkIcon,
  LocationOn as LocationIcon,
  AttachMoney as SalaryIcon,
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon
} from '@mui/icons-material';
import { zhTranslations } from '../translations/zh';

// Mock data for job recommendations
const mockJobs = [
  {
    id: 1,
    title: zhTranslations.jobRecommendations.mockData.titles[0],
    company: zhTranslations.jobRecommendations.mockData.companies[0],
    location: zhTranslations.jobRecommendations.mockData.locations[0],
    salary: zhTranslations.jobRecommendations.mockData.salaries[0],
    description: zhTranslations.jobRecommendations.mockData.descriptions[0],
    skills: zhTranslations.jobRecommendations.mockData.skills[0],
    rating: 4.5,
    bookmarked: false
  },
  {
    id: 2,
    title: zhTranslations.jobRecommendations.mockData.titles[1],
    company: zhTranslations.jobRecommendations.mockData.companies[1],
    location: zhTranslations.jobRecommendations.mockData.locations[1],
    salary: zhTranslations.jobRecommendations.mockData.salaries[1],
    description: zhTranslations.jobRecommendations.mockData.descriptions[1],
    skills: zhTranslations.jobRecommendations.mockData.skills[1],
    rating: 4.2,
    bookmarked: false
  },
  {
    id: 3,
    title: zhTranslations.jobRecommendations.mockData.titles[2],
    company: zhTranslations.jobRecommendations.mockData.companies[2],
    location: zhTranslations.jobRecommendations.mockData.locations[2],
    salary: zhTranslations.jobRecommendations.mockData.salaries[2],
    description: zhTranslations.jobRecommendations.mockData.descriptions[2],
    skills: zhTranslations.jobRecommendations.mockData.skills[2],
    rating: 4.7,
    bookmarked: false
  }
];

const JobRecommendations = () => {
  const [jobs, setJobs] = useState(mockJobs);
  const [searchTerm, setSearchTerm] = useState('');

  const handleBookmark = (id: number) => {
    setJobs(jobs.map(job =>
      job.id === id ? { ...job, bookmarked: !job.bookmarked } : job
    ));
  };

  const filteredJobs = jobs.filter(job =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <Box sx={{ maxWidth: 1200, width: '100%', px: 3, mt: 4 }}>
      <Typography variant="h1" align="center" gutterBottom>
        {zhTranslations.jobRecommendations.title}
      </Typography>
      <Typography variant="h3" align="center" sx={{ mb: 4, color: 'text.secondary' }}>
        {zhTranslations.jobRecommendations.subtitle}
      </Typography>

      <Box sx={{ mb: 4 }}>
        <TextField
          fullWidth
          placeholder={zhTranslations.jobRecommendations.searchPlaceholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              backgroundColor: 'rgba(17, 34, 64, 0.6)',
            }
          }}
        />
      </Box>

      <Grid container spacing={3}>
        {filteredJobs.map((job) => (
          <Grid item xs={12} key={job.id}>
            <Card sx={{
              transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 12px 20px rgba(0, 0, 0, 0.2)',
              }
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Box>
                    <Typography variant="h4" component="div">
                      {job.title}
                    </Typography>
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      {job.company}
                    </Typography>
                  </Box>
                  <IconButton
                    onClick={() => handleBookmark(job.id)}
                    color="primary"
                  >
                    {job.bookmarked ? <BookmarkIcon /> : <BookmarkBorderIcon />}
                  </IconButton>
                </Box>

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <LocationIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {job.location}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SalaryIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {job.salary}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Rating value={job.rating} precision={0.1} size="small" readOnly />
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                      ({job.rating})
                    </Typography>
                  </Box>
                </Box>

                <Typography variant="body1" sx={{ mb: 2 }}>
                  {job.description}
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {job.skills.map((skill) => (
                      <Chip
                        key={skill}
                        label={skill}
                        size="small"
                        sx={{
                          background: 'linear-gradient(45deg, rgba(0, 180, 216, 0.2) 30%, rgba(144, 224, 239, 0.2) 90%)',
                          border: '1px solid rgba(0, 180, 216, 0.3)',
                        }}
                      />
                    ))}
                  </Box>
                  <Button
                    variant="contained"
                    size="small"
                    sx={{
                      minWidth: '120px',
                    }}
                  >
                    {zhTranslations.jobRecommendations.applyNow}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default JobRecommendations;
