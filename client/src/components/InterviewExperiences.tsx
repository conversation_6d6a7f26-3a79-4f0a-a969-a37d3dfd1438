import { useState, useEffect } from 'react';
import { <PERSON>, Typo<PERSON>, Card, CardContent, Grid, Chip, Avatar, CircularProgress, Alert, Pagination } from '@mui/material';
import { Work as WorkIcon, Business as BusinessIcon } from '@mui/icons-material';
import { zhTranslations } from '../translations/zh';

// 定义面经数据接口
interface InterviewExperience {
  id: number;
  title: string;
  company: string;
  position: string;
  date: string;
  content: string;
  tags: string[];
  author?: string;
  authorId?: number;
  views?: number;
  likes?: number;
  result?: string;
  difficulty?: string;
  duration?: string;
  rounds?: number;
  user?: {
    id: number;
    username: string;
    fullName?: string;
    avatar?: string;
  };
}

// 定义API响应接口
interface ApiResponse {
  total: number;
  totalPages: number;
  currentPage: number;
  data: InterviewExperience[];
}

const InterviewExperiences = () => {
  // 状态管理
  const [experiences, setExperiences] = useState<InterviewExperience[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);

  // 从API获取面经数据
  useEffect(() => {
    const fetchExperiences = async () => {
      setLoading(true);
      setError(null);

      try {
        // 从localStorage获取认证token
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('未登录，请先登录');
        }

        // 调用API获取面经数据
        const response = await fetch(`http://localhost:3001/api/interview-experiences?page=${page}&limit=10`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('获取面经数据失败');
        }

        const data: ApiResponse = await response.json();

        // 更新状态
        setExperiences(data.data);
        setTotalPages(data.totalPages);
      } catch (err) {
        console.error('获取面经失败:', err);
        setError(err instanceof Error ? err.message : '获取面经数据失败');

        // 如果API调用失败，使用mock数据作为备用
        setExperiences([
          {
            id: 1,
            title: '科技创新公司面试经历',
            company: zhTranslations.interviewExperiences.mockData.companies[0],
            position: zhTranslations.interviewExperiences.mockData.positions[0],
            date: zhTranslations.interviewExperiences.mockData.dates[0],
            content: zhTranslations.interviewExperiences.mockData.contents[0],
            tags: zhTranslations.interviewExperiences.mockData.tags[0]
          },
          {
            id: 2,
            title: '数据分析公司面试经历',
            company: zhTranslations.interviewExperiences.mockData.companies[1],
            position: zhTranslations.interviewExperiences.mockData.positions[1],
            date: zhTranslations.interviewExperiences.mockData.dates[1],
            content: zhTranslations.interviewExperiences.mockData.contents[1],
            tags: zhTranslations.interviewExperiences.mockData.tags[1]
          },
          {
            id: 3,
            title: '云解决方案面试经历',
            company: zhTranslations.interviewExperiences.mockData.companies[2],
            position: zhTranslations.interviewExperiences.mockData.positions[2],
            date: zhTranslations.interviewExperiences.mockData.dates[2],
            content: zhTranslations.interviewExperiences.mockData.contents[2],
            tags: zhTranslations.interviewExperiences.mockData.tags[2]
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, [page]); // 当页码变化时重新获取数据

  // 处理分页变化
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  return (
    <Box sx={{ maxWidth: 1200, width: '100%', px: 3, mt: 4 }}>
      <Typography variant="h1" align="center" gutterBottom>
        {zhTranslations.interviewExperiences.title}
      </Typography>
      <Typography variant="h3" align="center" sx={{ mb: 6, color: 'text.secondary' }}>
        {zhTranslations.interviewExperiences.subtitle}
      </Typography>

      {/* 加载状态 */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}

      {/* 面经列表 */}
      {!loading && experiences.length === 0 && (
        <Alert severity="info" sx={{ mb: 4 }}>
          暂无面经数据
        </Alert>
      )}

      <Grid container spacing={4}>
        {experiences.map((experience) => (
          <Grid item xs={12} key={experience.id}>
            <Card sx={{
              transition: 'transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 12px 20px rgba(0, 0, 0, 0.2)',
              }
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <BusinessIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h5" component="div">
                      {experience.company}
                    </Typography>
                    <Typography variant="subtitle1" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                      <WorkIcon sx={{ fontSize: 16, mr: 0.5 }} />
                      {experience.position} • {new Date(experience.date).toLocaleDateString('zh-CN')}
                    </Typography>
                  </Box>
                </Box>

                {/* 标题 */}
                <Typography variant="h6" sx={{ mb: 2 }}>
                  {experience.title}
                </Typography>

                <Typography variant="body1" sx={{ mb: 3 }}>
                  {experience.content}
                </Typography>

                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {experience.tags && experience.tags.map((tag) => (
                    <Chip
                      key={tag}
                      label={tag}
                      size="small"
                      sx={{
                        background: 'linear-gradient(45deg, rgba(0, 180, 216, 0.2) 30%, rgba(144, 224, 239, 0.2) 90%)',
                        border: '1px solid rgba(0, 180, 216, 0.3)',
                      }}
                    />
                  ))}
                </Box>

                {/* 作者信息 */}
                {experience.user && (
                  <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                    <Typography variant="body2" color="text.secondary">
                      作者: {experience.user.fullName || experience.user.username}
                    </Typography>
                    {experience.user.avatar && (
                      <Avatar
                        src={experience.user.avatar}
                        sx={{ width: 24, height: 24, ml: 1 }}
                      />
                    )}
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 分页控件 */}
      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={handlePageChange}
            color="primary"
          />
        </Box>
      )}
    </Box>
  );

export default InterviewExperiences;
