import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Too<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  useMediaQuery,
  useTheme,
  Container,
  Slide,
  Fade,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Tooltip
} from '@mui/material';
import {
  Menu as MenuIcon,
  Person as PersonIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountCircleIcon,
  PersonAdd as PersonAddIcon
} from '@mui/icons-material';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import { zhTranslations } from '../translations/zh';

const navItems = [
  { name: zhTranslations.navItems.resumeOptimizer, path: '/' },
  { name: zhTranslations.navItems.interviewExperiences, path: '/interview-experiences' },
  { name: zhTranslations.navItems.jobRecommendations, path: '/job-recommendations' },
  { name: zhTranslations.navItems.interviewCoaching, path: '/interview-coaching' },
];

interface NavigationProps {
  isAuthenticated?: boolean;
  user?: any;
  onLogout?: () => void;
}

const Navigation = ({ isAuthenticated = false, user = null, onLogout = () => {} }: NavigationProps) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [prevScrollPos, setPrevScrollPos] = useState(0);
  const [visible, setVisible] = useState(true);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();
  const navigate = useNavigate();

  const isMenuOpen = Boolean(anchorEl);

  // 处理用户菜单打开
  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // 处理用户菜单关闭
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // 处理导航到个人资料页面
  const handleProfileClick = () => {
    handleMenuClose();
    navigate('/profile');
  };

  // 处理登出
  const handleLogoutClick = () => {
    handleMenuClose();
    onLogout();
    navigate('/');
  };

  // 处理导航栏滚动效果
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollPos = window.pageYOffset;

      // 设置导航栏背景透明度变化
      if (currentScrollPos > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }

      // 设置导航栏隐藏/显示
      setVisible((prevScrollPos > currentScrollPos) || currentScrollPos < 10);
      setPrevScrollPos(currentScrollPos);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [prevScrollPos]);

  // 关闭移动端抽屉
  useEffect(() => {
    setMobileOpen(false);
  }, [location.pathname]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const drawer = (
    <Box
      onClick={handleDrawerToggle}
      sx={{
        textAlign: 'center',
        height: '100%',
        background: 'linear-gradient(135deg, #0a192f 0%, #112240 100%)',
        display: 'flex',
        flexDirection: 'column',
        py: 2
      }}
    >
      <Fade in={true} timeout={800}>
        <Typography
          variant="h6"
          component={RouterLink}
          to="/"
          sx={{
            my: 2,
            textDecoration: 'none',
            color: 'inherit',
            fontWeight: 700,
            background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          {zhTranslations.appTitle}
        </Typography>
      </Fade>
      <List sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', gap: 1, pt: 4 }}>
        {navItems.map((item, index) => {
          const isActive = location.pathname === item.path ||
            (location.pathname === '/' && item.path === '/');

          return (
            <Fade
              key={item.name}
              in={true}
              timeout={500 + index * 100}
              style={{ transitionDelay: `${index * 50}ms` }}
            >
              <ListItem disablePadding>
                <ListItemButton
                  component={RouterLink}
                  to={item.path}
                  sx={{
                    textAlign: 'center',
                    borderRadius: 2,
                    mx: 2,
                    transition: 'all 0.3s ease',
                    position: 'relative',
                    background: isActive ? 'rgba(0, 180, 216, 0.1)' : 'transparent',
                    '&::after': isActive ? {
                      content: '""',
                      position: 'absolute',
                      bottom: '8px',
                      left: '30%',
                      width: '40%',
                      height: '2px',
                      background: 'linear-gradient(90deg, transparent, #00b4d8, transparent)',
                    } : {},
                    '&:hover': {
                      background: 'rgba(0, 180, 216, 0.1)',
                    }
                  }}
                >
                  <ListItemText
                    primary={item.name}
                    primaryTypographyProps={{
                      fontWeight: isActive ? 700 : 400,
                      color: isActive ? 'primary.main' : 'text.primary',
                    }}
                  />
                </ListItemButton>
              </ListItem>
            </Fade>
          );
        })}

        {/* 移动端登录/注册按钮 */}
        {!isAuthenticated ? (
          <>
            <Divider sx={{ my: 2, borderColor: 'rgba(255, 255, 255, 0.1)' }} />
            <Fade in={true} timeout={800} style={{ transitionDelay: '400ms' }}>
              <ListItem disablePadding>
                <ListItemButton
                  component={RouterLink}
                  to="/login"
                  sx={{
                    textAlign: 'center',
                    borderRadius: 2,
                    mx: 2,
                    mb: 1,
                    transition: 'all 0.3s ease',
                    background: 'rgba(0, 180, 216, 0.1)',
                    '&:hover': {
                      background: 'rgba(0, 180, 216, 0.2)',
                    }
                  }}
                >
                  <LoginIcon sx={{ mr: 1, color: 'primary.light' }} />
                  <ListItemText
                    primary="登录"
                    primaryTypographyProps={{
                      fontWeight: 600,
                      color: 'primary.light',
                    }}
                  />
                </ListItemButton>
              </ListItem>
            </Fade>
            <Fade in={true} timeout={800} style={{ transitionDelay: '500ms' }}>
              <ListItem disablePadding>
                <ListItemButton
                  component={RouterLink}
                  to="/register"
                  sx={{
                    textAlign: 'center',
                    borderRadius: 2,
                    mx: 2,
                    background: 'linear-gradient(45deg, rgba(0, 180, 216, 0.2) 30%, rgba(144, 224, 239, 0.2) 90%)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, rgba(0, 180, 216, 0.3) 30%, rgba(144, 224, 239, 0.3) 90%)',
                    }
                  }}
                >
                  <PersonAddIcon sx={{ mr: 1, color: 'primary.light' }} />
                  <ListItemText
                    primary="注册"
                    primaryTypographyProps={{
                      fontWeight: 600,
                      color: 'primary.light',
                    }}
                  />
                </ListItemButton>
              </ListItem>
            </Fade>
          </>
        ) : (
          <>
            <Divider sx={{ my: 2, borderColor: 'rgba(255, 255, 255, 0.1)' }} />
            <Fade in={true} timeout={800} style={{ transitionDelay: '400ms' }}>
              <ListItem disablePadding>
                <ListItemButton
                  component={RouterLink}
                  to="/profile"
                  sx={{
                    textAlign: 'center',
                    borderRadius: 2,
                    mx: 2,
                    mb: 1,
                    transition: 'all 0.3s ease',
                    background: 'rgba(0, 180, 216, 0.1)',
                    '&:hover': {
                      background: 'rgba(0, 180, 216, 0.2)',
                    }
                  }}
                >
                  <PersonIcon sx={{ mr: 1, color: 'primary.light' }} />
                  <ListItemText
                    primary="个人资料"
                    primaryTypographyProps={{
                      fontWeight: 600,
                      color: 'primary.light',
                    }}
                  />
                </ListItemButton>
              </ListItem>
            </Fade>
            <Fade in={true} timeout={800} style={{ transitionDelay: '500ms' }}>
              <ListItem disablePadding>
                <ListItemButton
                  onClick={handleLogoutClick}
                  sx={{
                    textAlign: 'center',
                    borderRadius: 2,
                    mx: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      background: 'rgba(255, 93, 143, 0.1)',
                    }
                  }}
                >
                  <LogoutIcon sx={{ mr: 1, color: 'error.light' }} />
                  <ListItemText
                    primary="退出登录"
                    primaryTypographyProps={{
                      fontWeight: 600,
                      color: 'error.light',
                    }}
                  />
                </ListItemButton>
              </ListItem>
            </Fade>
          </>
        )}
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <Slide appear={false} direction="down" in={visible}>
        <AppBar
          component="nav"
          position="fixed"
          elevation={scrolled ? 4 : 0}
          sx={{
            background: scrolled
              ? 'rgba(10, 25, 47, 0.9)'
              : 'rgba(10, 25, 47, 0.5)',
            backdropFilter: 'blur(10px)',
            boxShadow: scrolled
              ? '0 4px 30px rgba(0, 0, 0, 0.15)'
              : 'none',
            transition: 'all 0.3s ease',
            borderBottom: scrolled
              ? '1px solid rgba(255, 255, 255, 0.05)'
              : 'none',
          }}
        >
          <Container maxWidth="xl">
            <Toolbar sx={{ px: { xs: 1, sm: 2 } }}>
              <Typography
                variant="h6"
                component={RouterLink}
                to="/"
                sx={{
                  flexGrow: 1,
                  display: { xs: 'none', sm: 'block' },
                  textDecoration: 'none',
                  color: 'inherit',
                  fontWeight: 700,
                  fontSize: { sm: '1.2rem', md: '1.4rem' },
                  background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  letterSpacing: '-0.02em',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'scale(1.05)',
                    textShadow: '0 0 20px rgba(0, 180, 216, 0.5)',
                  }
                }}
              >
                {zhTranslations.appTitle}
              </Typography>
              <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 0.5, alignItems: 'center' }}>
                {navItems.map((item, index) => {
                  const isActive = location.pathname === item.path ||
                    (location.pathname === '/' && item.path === '/');

                  return (
                    <Tooltip
                      key={item.name}
                      title={!isAuthenticated ? "需要登录" : ""}
                      arrow
                      placement="bottom"
                      disableHoverListener={isAuthenticated}
                    >
                      <span>
                        <Button
                          component={RouterLink}
                          to={item.path}
                      sx={{
                        color: isActive ? 'primary.main' : 'white',
                        fontWeight: isActive ? 600 : 400,
                        mx: 0.5,
                        px: 2,
                        py: 1,
                        borderRadius: 2,
                        position: 'relative',
                        overflow: 'hidden',
                        transition: 'all 0.3s ease',
                        background: isActive ? 'rgba(0, 180, 216, 0.1)' : 'transparent',
                        '&::after': isActive ? {
                          content: '""',
                          position: 'absolute',
                          bottom: '6px',
                          left: '25%',
                          width: '50%',
                          height: '2px',
                          background: 'linear-gradient(90deg, transparent, #00b4d8, transparent)',
                        } : {},
                        '&:hover': {
                          background: 'rgba(0, 180, 216, 0.1)',
                          transform: 'translateY(-2px)',
                        }
                      }}
                    >
                      {item.name}
                        </Button>
                      </span>
                    </Tooltip>
                  );
                })}

                {/* 用户菜单 */}
                {isAuthenticated ? (
                  <Box sx={{ ml: 1 }}>
                    <IconButton
                      onClick={handleProfileMenuOpen}
                      size="medium"
                      edge="end"
                      aria-label="account of current user"
                      aria-haspopup="true"
                      color="inherit"
                      sx={{
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          background: 'rgba(0, 180, 216, 0.1)',
                          transform: 'scale(1.05)',
                        }
                      }}
                    >
                      {user?.avatar ? (
                        <Avatar
                          src={user.avatar}
                          alt={user.username}
                          sx={{
                            width: 32,
                            height: 32,
                            border: '2px solid',
                            borderColor: 'primary.main',
                          }}
                        />
                      ) : (
                        <Avatar
                          sx={{
                            width: 32,
                            height: 32,
                            bgcolor: 'primary.main',
                            color: 'white',
                            fontWeight: 'bold',
                            border: '2px solid',
                            borderColor: 'primary.light',
                          }}
                        >
                          {user?.username?.charAt(0).toUpperCase() || user?.fullName?.charAt(0).toUpperCase() || 'U'}
                        </Avatar>
                      )}
                    </IconButton>
                    <Menu
                      anchorEl={anchorEl}
                      id="account-menu"
                      open={isMenuOpen}
                      onClose={handleMenuClose}
                      onClick={handleMenuClose}
                      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                      PaperProps={{
                        sx: {
                          mt: 1.5,
                          background: 'rgba(17, 34, 64, 0.95)',
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255, 255, 255, 0.1)',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                          borderRadius: 2,
                          minWidth: 180,
                          '& .MuiMenuItem-root': {
                            px: 2,
                            py: 1.5,
                            borderRadius: 1,
                            mx: 0.5,
                            my: 0.5,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              background: 'rgba(0, 180, 216, 0.1)',
                            },
                          },
                        },
                      }}
                    >
                      <Box sx={{ px: 2, py: 1 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          {user?.fullName || user?.username}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                          {user?.email}
                        </Typography>
                      </Box>
                      <Divider sx={{ my: 1, borderColor: 'rgba(255, 255, 255, 0.1)' }} />
                      <MenuItem onClick={handleProfileClick}>
                        <PersonIcon sx={{ mr: 2, color: 'primary.light' }} />
                        个人资料
                      </MenuItem>
                      <MenuItem onClick={handleLogoutClick}>
                        <LogoutIcon sx={{ mr: 2, color: 'error.light' }} />
                        退出登录
                      </MenuItem>
                    </Menu>
                  </Box>
                ) : (
                  <Box sx={{ display: 'flex', ml: 1 }}>
                    <Button
                      component={RouterLink}
                      to="/login"
                      startIcon={<LoginIcon />}
                      variant="outlined"
                      size="small"
                      sx={{
                        borderRadius: 2,
                        mr: 1,
                        borderWidth: '2px',
                        '&:hover': {
                          borderWidth: '2px',
                          transform: 'translateY(-2px)',
                        }
                      }}
                    >
                      登录
                    </Button>
                    <Button
                      component={RouterLink}
                      to="/register"
                      startIcon={<PersonAddIcon />}
                      variant="contained"
                      size="small"
                      sx={{
                        borderRadius: 2,
                        background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #0096c7 30%, #48cae4 90%)',
                          transform: 'translateY(-2px)',
                        }
                      }}
                    >
                      注册
                    </Button>
                  </Box>
                )}
              </Box>

              {/* 移动端菜单按钮 */}
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {isAuthenticated && (
                  <IconButton
                    onClick={handleProfileMenuOpen}
                    size="medium"
                    edge="end"
                    aria-label="account of current user"
                    aria-haspopup="true"
                    color="inherit"
                    sx={{
                      display: { xs: 'flex', md: 'none' },
                      mr: 1,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        background: 'rgba(0, 180, 216, 0.1)',
                      }
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 28,
                        height: 28,
                        bgcolor: 'primary.main',
                        fontSize: '0.8rem',
                      }}
                    >
                      {user?.username?.charAt(0).toUpperCase() || 'U'}
                    </Avatar>
                  </IconButton>
                )}
                <IconButton
                  color="inherit"
                  aria-label="open drawer"
                  edge="end"
                  onClick={handleDrawerToggle}
                  sx={{
                    display: { md: 'none' },
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      background: 'rgba(0, 180, 216, 0.1)',
                      transform: 'rotate(90deg)',
                    }
                  }}
                >
                  <MenuIcon />
                </IconButton>
              </Box>
            </Toolbar>
          </Container>
        </AppBar>
      </Slide>
      <Box component="nav">
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: { xs: '85%', sm: 320 },
              borderRadius: '0 16px 16px 0',
              border: 'none',
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>
      <Box component="main" sx={{ width: '100%' }}>
        <Toolbar /> {/* This creates space below the AppBar */}
      </Box>
    </Box>
  );
};

export default Navigation;
