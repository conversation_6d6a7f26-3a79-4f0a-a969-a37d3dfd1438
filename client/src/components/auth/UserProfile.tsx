import { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Button,
  TextField,
  Grid,
  Divider,
  Alert,
  Fade,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Person as PersonIcon,
  History as HistoryIcon,
  Bookmark as BookmarkIcon,
  Phone as PhoneIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { zhTranslations } from '../../translations/zh';
import PhoneVerification from './PhoneVerification';

interface UserData {
  id: number;
  username: string;
  email: string;
  phoneNumber?: string;
  phoneVerified?: boolean;
  fullName?: string;
  avatar?: string;
  bio?: string;
  role: string;
  lastLogin?: string;
  createdAt: string;
}

interface UserProfileProps {
  token: string;
  onLogout: () => void;
}

const UserProfile = ({ token, onLogout }: UserProfileProps) => {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [fullName, setFullName] = useState('');
  const [bio, setBio] = useState('');
  const [avatar, setAvatar] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [successMessage, setSuccessMessage] = useState('');
  const [userExperiences, setUserExperiences] = useState<any[]>([]);
  const [experiencesLoading, setExperiencesLoading] = useState(false);

  // 手机绑定相关状态
  const [phoneDialogOpen, setPhoneDialogOpen] = useState(false);
  const [phoneBindingSuccess, setPhoneBindingSuccess] = useState(false);

  // 获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/auth/me', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('获取用户信息失败');
        }

        const data = await response.json();
        setUserData(data);
        setFullName(data.fullName || '');
        setBio(data.bio || '');
        setAvatar(data.avatar || '');
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取用户信息失败');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [token]);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('http://localhost:3001/api/auth/me', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          fullName,
          bio,
          avatar
        })
      });

      if (!response.ok) {
        throw new Error('更新用户信息失败');
      }

      const data = await response.json();
      setUserData(data.user);
      setEditMode(false);
      setSuccessMessage('个人信息已更新');

      // 3秒后清除成功消息
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新用户信息失败');
    }
  };

  // 处理标签切换
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);

    // 如果切换到"我的面经"标签，加载用户面经
    if (newValue === 1) {
      fetchUserExperiences();
    }
  };

  // 打开手机绑定对话框
  const handleOpenPhoneDialog = () => {
    setPhoneDialogOpen(true);
    setPhoneBindingSuccess(false);
  };

  // 关闭手机绑定对话框
  const handleClosePhoneDialog = () => {
    setPhoneDialogOpen(false);

    // 如果绑定成功，刷新用户数据
    if (phoneBindingSuccess) {
      fetchUserData();
    }
  };

  // 处理手机验证成功
  const handlePhoneVerified = async (phoneNumber: string, smsCode: string) => {
    try {
      const response = await fetch('http://localhost:3001/api/auth/bind-phone', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ phoneNumber, smsCode })
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || '绑定手机号失败');
      }

      setPhoneBindingSuccess(true);
      setSuccessMessage('手机号绑定成功');

      // 3秒后清除成功消息
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : '绑定手机号失败');
    }
  };

  // 获取用户面经
  const fetchUserExperiences = async () => {
    if (userExperiences.length > 0) return; // 已加载过，不重复加载

    setExperiencesLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/interview-experiences/user/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('获取面经失败');
      }

      const data = await response.json();
      setUserExperiences(data);
    } catch (err) {
      console.error('获取用户面经失败:', err);
      // 不显示错误，只在控制台记录
    } finally {
      setExperiencesLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 8 }}>
        <Typography>加载中...</Typography>
      </Box>
    );
  }

  if (!userData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 8 }}>
        <Typography>未找到用户信息</Typography>
      </Box>
    );
  }

  return (
    <Fade in={true} timeout={800}>
      <Box sx={{ maxWidth: 900, mx: 'auto', mt: 4, px: 2, mb: 8 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {successMessage && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {successMessage}
          </Alert>
        )}

        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Card sx={{
              borderRadius: 3,
              boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
              height: '100%'
            }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <Avatar
                  src={userData.avatar}
                  alt={userData.username}
                  sx={{
                    width: 120,
                    height: 120,
                    mx: 'auto',
                    mb: 2,
                    border: '4px solid',
                    borderColor: 'primary.main',
                    boxShadow: '0 4px 20px rgba(0, 180, 216, 0.4)',
                  }}
                >
                  {userData.fullName ? userData.fullName[0] : userData.username[0]}
                </Avatar>

                <Typography variant="h5" gutterBottom>
                  {userData.fullName || userData.username}
                </Typography>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  @{userData.username}
                </Typography>

                <Chip
                  label={userData.role === 'admin' ? '管理员' : '用户'}
                  color={userData.role === 'admin' ? 'secondary' : 'primary'}
                  size="small"
                  sx={{ mt: 1, mb: 2 }}
                />

                <Divider sx={{ my: 2 }} />

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  邮箱: {userData.email}
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  注册时间: {new Date(userData.createdAt).toLocaleDateString()}
                </Typography>

                {userData.lastLogin && (
                  <Typography variant="body2" color="text.secondary">
                    上次登录: {new Date(userData.lastLogin).toLocaleString()}
                  </Typography>
                )}

                <Button
                  variant="outlined"
                  color="error"
                  fullWidth
                  onClick={onLogout}
                  sx={{ mt: 3 }}
                >
                  退出登录
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={8}>
            <Card sx={{
              borderRadius: 3,
              boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                  <Tabs value={tabValue} onChange={handleTabChange}>
                    <Tab icon={<PersonIcon />} label="个人资料" />
                    <Tab icon={<HistoryIcon />} label="我的面经" />
                    <Tab icon={<BookmarkIcon />} label="收藏" />
                  </Tabs>
                </Box>

                {/* 个人资料标签页 */}
                {tabValue === 0 && (
                  <Box component="form" onSubmit={handleSubmit}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                      <Typography variant="h6">个人资料</Typography>
                      <Button
                        startIcon={editMode ? <SaveIcon /> : <EditIcon />}
                        color="primary"
                        onClick={() => editMode ? handleSubmit : setEditMode(true)}
                        type={editMode ? 'submit' : 'button'}
                      >
                        {editMode ? '保存' : '编辑'}
                      </Button>
                    </Box>

                    {editMode ? (
                      <Grid container spacing={3}>
                        <Grid item xs={12}>
                          <TextField
                            label="姓名"
                            fullWidth
                            value={fullName}
                            onChange={(e) => setFullName(e.target.value)}
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            label="头像URL"
                            fullWidth
                            value={avatar}
                            onChange={(e) => setAvatar(e.target.value)}
                            helperText="输入头像图片的URL地址"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            label="个人简介"
                            fullWidth
                            multiline
                            rows={4}
                            value={bio}
                            onChange={(e) => setBio(e.target.value)}
                          />
                        </Grid>
                      </Grid>
                    ) : (
                      <Box>
                        <Typography variant="body1" paragraph>
                          <strong>姓名:</strong> {userData.fullName || '未设置'}
                        </Typography>
                        <Typography variant="body1" paragraph>
                          <strong>电子邮箱:</strong> {userData.email}
                        </Typography>
                        <Typography variant="body1" paragraph sx={{ display: 'flex', alignItems: 'center' }}>
                          <strong>手机号码:</strong> {userData.phoneNumber ? (
                            <>
                              {userData.phoneNumber}
                              {userData.phoneVerified && (
                                <Chip
                                  label="已验证"
                                  color="success"
                                  size="small"
                                  sx={{ ml: 1 }}
                                />
                              )}
                            </>
                          ) : '未绑定'}
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<PhoneIcon />}
                            onClick={handleOpenPhoneDialog}
                            sx={{ ml: 2 }}
                          >
                            {userData.phoneNumber ? '修改' : '绑定手机号'}
                          </Button>
                        </Typography>
                        <Typography variant="body1" paragraph>
                          <strong>个人简介:</strong> {userData.bio || '未设置个人简介'}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}

                {/* 我的面经标签页 */}
                {tabValue === 1 && (
                  <Box>
                    <Typography variant="h6" sx={{ mb: 2 }}>我的面经</Typography>

                    {experiencesLoading ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                        <Typography>加载中...</Typography>
                      </Box>
                    ) : userExperiences.length > 0 ? (
                      <List>
                        {userExperiences.map((experience) => (
                          <ListItem
                            key={experience.id}
                            sx={{
                              bgcolor: 'background.paper',
                              mb: 2,
                              borderRadius: 2,
                              flexDirection: 'column',
                              alignItems: 'flex-start',
                              p: 2
                            }}
                          >
                            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                {experience.title}
                              </Typography>
                              <Chip
                                label={experience.result}
                                size="small"
                                color={
                                  experience.result === '通过' ? 'success' :
                                  experience.result === '未通过' ? 'error' :
                                  'default'
                                }
                              />
                            </Box>

                            <Box sx={{ width: '100%', display: 'flex', mb: 1 }}>
                              <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                                公司: {experience.company}
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                                职位: {experience.position}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                难度: {experience.difficulty}
                              </Typography>
                            </Box>

                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                              发布时间: {new Date(experience.createdAt).toLocaleDateString()}
                            </Typography>

                            <Box sx={{ width: '100%', mt: 1 }}>
                              <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                                {experience.tags && experience.tags.map((tag: string) => (
                                  <Chip
                                    key={tag}
                                    label={tag}
                                    size="small"
                                    variant="outlined"
                                    sx={{ mb: 0.5 }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <List>
                        <ListItem sx={{ bgcolor: 'background.paper', mb: 2, borderRadius: 2 }}>
                          <ListItemText
                            primary="暂无面经"
                            secondary="您还没有发布过面经"
                          />
                        </ListItem>
                      </List>
                    )}
                  </Box>
                )}

                {/* 收藏标签页 */}
                {tabValue === 2 && (
                  <Box>
                    <Typography variant="h6" sx={{ mb: 2 }}>我的收藏</Typography>
                    <List>
                      <ListItem sx={{ bgcolor: 'background.paper', mb: 2, borderRadius: 2 }}>
                        <ListItemText
                          primary="暂无收藏"
                          secondary="您还没有收藏任何内容"
                        />
                      </ListItem>
                    </List>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* 手机绑定对话框 */}
        <Dialog open={phoneDialogOpen} onClose={handleClosePhoneDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {phoneBindingSuccess ? '绑定成功' : '绑定手机号'}
          </DialogTitle>
          <DialogContent>
            {phoneBindingSuccess ? (
              <Alert severity="success" sx={{ mt: 2 }}>
                手机号绑定成功！您现在可以使用手机号和验证码登录，也可以通过手机找回密码。
              </Alert>
            ) : (
              <Box sx={{ py: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  绑定手机号可以提高账号安全性，您可以使用手机号和验证码登录，也可以通过手机找回密码。
                </Typography>
                <PhoneVerification
                  onVerified={handlePhoneVerified}
                  initialPhoneNumber={userData?.phoneNumber || ''}
                />
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClosePhoneDialog}>
              {phoneBindingSuccess ? '关闭' : '取消'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Fade>
  );
};

export default UserProfile;
