import { ReactNode, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import LoginPrompt from './LoginPrompt';

interface ProtectedRouteProps {
  isAuthenticated: boolean;
  authenticationChecked: boolean;
  children: ReactNode;
  loginPath?: string;
}

/**
 * 受保护的路由组件
 * 如果用户已登录，则渲染子组件
 * 如果用户未登录，则重定向到登录页面
 */
const ProtectedRoute = ({
  isAuthenticated,
  authenticationChecked,
  children,
  loginPath = '/login'
}: ProtectedRouteProps) => {
  const location = useLocation();
  const [showLoading, setShowLoading] = useState(true);

  // 如果认证检查时间超过500ms，显示加载状态
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // 如果认证状态尚未检查完成，显示加载状态
  if (!authenticationChecked) {
    return showLoading ? (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body1" sx={{ mt: 2 }}>
          正在验证登录状态...
        </Typography>
      </Box>
    ) : null;
  }

  // 如果用户未登录，显示登录提示
  if (!isAuthenticated) {
    return <LoginPrompt redirectPath={location.pathname} />;
  }

  // 如果用户已登录，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;
