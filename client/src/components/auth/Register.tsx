import { useState } from 'react';
import {
  <PERSON>,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  Link,
  Fade,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  Divider
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  PersonAdd as PersonAddIcon,
  ArrowForward as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { zhTranslations } from '../../translations/zh';
import PhoneVerification from './PhoneVerification';

interface RegisterProps {
  onRegisterSuccess: (userData: any, token: string) => void;
}

const Register = ({ onRegisterSuccess }: RegisterProps) => {
  // 表单字段
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [smsCode, setSmsCode] = useState('');

  // UI状态
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [phoneVerified, setPhoneVerified] = useState(false);

  const navigate = useNavigate();

  // 步骤标题
  const steps = ['基本信息', '手机验证', '完成注册'];

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  // 处理下一步
  const handleNext = () => {
    // 验证当前步骤
    if (activeStep === 0) {
      // 验证基本信息
      if (!username || !email || !password || !confirmPassword) {
        setError('请填写所有必填字段');
        return;
      }

      if (username.length < 3) {
        setError('用户名至少需要3个字符');
        return;
      }

      if (!validateEmail(email)) {
        setError('请输入有效的电子邮箱地址');
        return;
      }

      if (password.length < 6) {
        setError('密码至少需要6个字符');
        return;
      }

      if (password !== confirmPassword) {
        setError('两次输入的密码不一致');
        return;
      }
    } else if (activeStep === 1) {
      // 验证手机验证
      if (!phoneVerified) {
        setError('请完成手机验证');
        return;
      }
    }

    // 清除错误
    setError('');

    // 如果是最后一步，提交注册
    if (activeStep === steps.length - 1) {
      handleSubmit();
    } else {
      // 否则进入下一步
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  // 处理上一步
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
    setError('');
  };

  // 处理手机验证成功
  const handlePhoneVerified = (verifiedPhone: string, verifiedCode: string) => {
    setPhoneNumber(verifiedPhone);
    setSmsCode(verifiedCode);
    setPhoneVerified(true);
    setActiveStep(2); // 直接进入最后一步
  };

  // 提交注册
  const handleSubmit = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:3001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          email,
          password,
          fullName,
          phoneNumber,
          smsCode
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '注册失败');
      }

      // 保存令牌到本地存储
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data.user));

      // 调用注册成功回调
      onRegisterSuccess(data.user, data.token);

      // 重定向到首页
      navigate('/');
    } catch (err) {
      setError(err instanceof Error ? err.message : '注册失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染步骤内容
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <TextField
              label={zhTranslations.auth?.register?.usernameLabel || '用户名'}
              variant="outlined"
              fullWidth
              required
              margin="normal"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              sx={{ mb: 2 }}
              helperText="用户名至少需要3个字符"
            />

            <TextField
              label={zhTranslations.auth?.register?.emailLabel || '电子邮箱'}
              type="email"
              variant="outlined"
              fullWidth
              required
              margin="normal"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              sx={{ mb: 2 }}
            />

            <TextField
              label={zhTranslations.auth?.register?.fullNameLabel || '姓名（可选）'}
              variant="outlined"
              fullWidth
              margin="normal"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              sx={{ mb: 2 }}
            />

            <TextField
              label={zhTranslations.auth?.register?.passwordLabel || '密码'}
              type={showPassword ? 'text' : 'password'}
              variant="outlined"
              fullWidth
              required
              margin="normal"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
              helperText="密码至少需要6个字符"
            />

            <TextField
              label={zhTranslations.auth?.register?.confirmPasswordLabel || '确认密码'}
              type={showPassword ? 'text' : 'password'}
              variant="outlined"
              fullWidth
              required
              margin="normal"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              sx={{ mb: 3 }}
            />
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              为了保障账号安全，请验证您的手机号码。验证成功后，您可以使用手机号登录，也可以通过手机号找回密码。
            </Typography>
            <PhoneVerification onVerified={handlePhoneVerified} />
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              注册信息确认
            </Typography>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" gutterBottom>
                <strong>用户名:</strong> {username}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>电子邮箱:</strong> {email}
              </Typography>
              {fullName && (
                <Typography variant="body1" gutterBottom>
                  <strong>姓名:</strong> {fullName}
                </Typography>
              )}
              {phoneVerified && (
                <Typography variant="body1" gutterBottom>
                  <strong>手机号:</strong> {phoneNumber} (已验证)
                </Typography>
              )}
            </Box>
            <Alert severity="info" sx={{ mb: 3 }}>
              点击"完成注册"按钮，即表示您同意我们的服务条款和隐私政策。
            </Alert>
          </Box>
        );
      default:
        return '未知步骤';
    }
  };

  return (
    <Fade in={true} timeout={800}>
      <Box sx={{
        maxWidth: 600,
        mx: 'auto',
        mt: 8,
        px: 2,
        mb: 8
      }}>
        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
          position: 'relative',
          overflow: 'visible',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: '-10px',
            left: '-10px',
            right: '-10px',
            bottom: '-10px',
            background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.2) 0%, rgba(144, 224, 239, 0.1) 100%)',
            borderRadius: '20px',
            filter: 'blur(20px)',
            opacity: 0.7,
            zIndex: -1,
          },
        }}>
          <CardContent sx={{ p: 4 }}>
            <Typography
              variant="h4"
              align="center"
              gutterBottom
              sx={{
                color: 'primary.main',
                fontWeight: 700,
                mb: 3
              }}
            >
              {zhTranslations.auth?.register?.title || '用户注册'}
            </Typography>

            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>

            {error && (
              <Alert
                severity="error"
                sx={{ mb: 3 }}
                variant="filled"
              >
                {error}
              </Alert>
            )}

            <Box>
              {getStepContent(activeStep)}

              <Divider sx={{ my: 3 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  disabled={activeStep === 0 || loading}
                  onClick={handleBack}
                  startIcon={<ArrowBackIcon />}
                >
                  上一步
                </Button>

                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={loading || (activeStep === 1 && !phoneVerified)}
                  endIcon={activeStep === steps.length - 1 ? <PersonAddIcon /> : <ArrowForwardIcon />}
                  sx={{
                    background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                    boxShadow: '0 4px 20px rgba(0, 180, 216, 0.4)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #0096c7 30%, #48cae4 90%)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 6px 25px rgba(0, 180, 216, 0.5)',
                    },
                  }}
                >
                  {loading
                    ? '处理中...'
                    : activeStep === steps.length - 1
                      ? '完成注册'
                      : '下一步'
                  }
                </Button>
              </Box>

              <Box sx={{ textAlign: 'center', mt: 3 }}>
                <Typography variant="body2">
                  {zhTranslations.auth?.register?.hasAccount || '已有账号？'}{' '}
                  <Link
                    component={RouterLink}
                    to="/login"
                    sx={{
                      fontWeight: 600,
                      color: 'primary.main',
                      textDecoration: 'none',
                      '&:hover': {
                        textDecoration: 'underline',
                      }
                    }}
                  >
                    {zhTranslations.auth?.register?.loginLink || '立即登录'}
                  </Link>
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Fade>
  );
};

export default Register;
