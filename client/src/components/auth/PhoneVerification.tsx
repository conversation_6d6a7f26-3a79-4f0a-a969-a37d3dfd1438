import { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  InputAdornment,
  CircularProgress,
  Alert
} from '@mui/material';
import { Phone as PhoneIcon, Send as SendIcon } from '@mui/icons-material';

interface PhoneVerificationProps {
  onVerified: (phoneNumber: string, smsCode: string) => void;
  initialPhoneNumber?: string;
}

const PhoneVerification = ({ onVerified, initialPhoneNumber = '' }: PhoneVerificationProps) => {
  const [phoneNumber, setPhoneNumber] = useState(initialPhoneNumber);
  const [smsCode, setSmsCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 处理倒计时
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // 验证手机号格式
  const isValidPhoneNumber = (phone: string) => {
    const regex = /^1[3-9]\d{9}$/;
    return regex.test(phone);
  };

  // 发送验证码
  const handleSendCode = async () => {
    // 清除之前的错误和成功消息
    setError('');
    setSuccess('');

    // 验证手机号
    if (!phoneNumber) {
      setError('请输入手机号');
      return;
    }

    if (!isValidPhoneNumber(phoneNumber)) {
      setError('请输入有效的手机号码');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('http://localhost:3001/api/sms/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '发送验证码失败');
      }

      // 设置成功消息
      setSuccess('验证码已发送，请查收短信');
      
      // 开始倒计时
      setCountdown(60);
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送验证码失败');
    } finally {
      setLoading(false);
    }
  };

  // 验证码输入处理
  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 只允许输入数字，最多6位
    const value = e.target.value.replace(/[^0-9]/g, '').slice(0, 6);
    setSmsCode(value);
  };

  // 提交验证
  const handleSubmit = () => {
    // 清除之前的错误
    setError('');

    // 验证手机号和验证码
    if (!phoneNumber) {
      setError('请输入手机号');
      return;
    }

    if (!isValidPhoneNumber(phoneNumber)) {
      setError('请输入有效的手机号码');
      return;
    }

    if (!smsCode) {
      setError('请输入验证码');
      return;
    }

    if (smsCode.length !== 6) {
      setError('验证码应为6位数字');
      return;
    }

    // 调用回调函数
    onVerified(phoneNumber, smsCode);
  };

  return (
    <Box sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Typography variant="subtitle1" gutterBottom>
        手机验证
      </Typography>

      <Box sx={{ display: 'flex', mb: 2 }}>
        <TextField
          label="手机号"
          variant="outlined"
          fullWidth
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
          disabled={loading || countdown > 0}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <PhoneIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mr: 1 }}
        />
        <Button
          variant="contained"
          color="primary"
          onClick={handleSendCode}
          disabled={loading || countdown > 0 || !isValidPhoneNumber(phoneNumber)}
          sx={{ minWidth: '120px', whiteSpace: 'nowrap' }}
        >
          {loading ? (
            <CircularProgress size={24} color="inherit" />
          ) : countdown > 0 ? (
            `${countdown}秒后重发`
          ) : (
            <>
              <SendIcon sx={{ mr: 0.5 }} />
              发送验证码
            </>
          )}
        </Button>
      </Box>

      <TextField
        label="验证码"
        variant="outlined"
        fullWidth
        value={smsCode}
        onChange={handleCodeChange}
        placeholder="请输入6位验证码"
        sx={{ mb: 2 }}
        inputProps={{ maxLength: 6 }}
      />

      <Button
        variant="contained"
        color="primary"
        fullWidth
        onClick={handleSubmit}
        disabled={!isValidPhoneNumber(phoneNumber) || smsCode.length !== 6}
      >
        验证
      </Button>
    </Box>
  );
};

export default PhoneVerification;
