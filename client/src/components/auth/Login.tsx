import { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Alert,
  InputAdornment,
  IconButton,
  Link,
  Fade,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Login as LoginIcon,
  Person as PersonIcon,
  Phone as PhoneIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { zhTranslations } from '../../translations/zh';
import PhoneVerification from './PhoneVerification';

interface LoginProps {
  onLoginSuccess: (userData: any, token: string) => void;
}

const Login = ({ onLoginSuccess }: LoginProps) => {
  // 表单字段
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [smsCode, setSmsCode] = useState('');

  // UI状态
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [phoneVerified, setPhoneVerified] = useState(false);

  const navigate = useNavigate();
  const location = useLocation();

  // 获取重定向路径（如果有）
  const from = location.state?.from || '/';

  // 处理标签切换
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setError('');
  };

  // 处理手机验证成功
  const handlePhoneVerified = (verifiedPhone: string, verifiedCode: string) => {
    setPhoneNumber(verifiedPhone);
    setSmsCode(verifiedCode);
    setPhoneVerified(true);
    handleLogin(true);
  };

  // 处理账号密码登录表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      setError('请输入用户名和密码');
      return;
    }

    handleLogin(false);
  };

  // 处理登录请求
  const handleLogin = async (isPhoneLogin: boolean) => {
    setLoading(true);
    setError('');

    try {
      // 准备请求体
      let requestBody;

      if (isPhoneLogin) {
        if (!phoneNumber || !smsCode) {
          setError('请完成手机验证');
          setLoading(false);
          return;
        }
        requestBody = { phoneNumber, smsCode };
      } else {
        requestBody = { username, password };
      }

      const response = await fetch('http://localhost:3001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || '登录失败');
      }

      // 保存令牌到本地存储
      localStorage.setItem('token', data.token);
      localStorage.setItem('user', JSON.stringify(data.user));

      // 调用登录成功回调
      onLoginSuccess(data.user, data.token);

      // 重定向到之前尝试访问的页面或首页
      navigate(from);
    } catch (err) {
      setError(err instanceof Error ? err.message : '登录失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Fade in={true} timeout={800}>
      <Box sx={{
        maxWidth: 500,
        mx: 'auto',
        mt: 8,
        px: 2
      }}>
        <Card sx={{
          borderRadius: 3,
          boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
          position: 'relative',
          overflow: 'visible',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: '-10px',
            left: '-10px',
            right: '-10px',
            bottom: '-10px',
            background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.2) 0%, rgba(144, 224, 239, 0.1) 100%)',
            borderRadius: '20px',
            filter: 'blur(20px)',
            opacity: 0.7,
            zIndex: -1,
          },
        }}>
          <CardContent sx={{ p: 4 }}>
            <Typography
              variant="h4"
              align="center"
              gutterBottom
              sx={{
                color: 'primary.main',
                fontWeight: 700,
                mb: 3
              }}
            >
              {zhTranslations.auth?.login?.title || '用户登录'}
            </Typography>

            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{ mb: 3 }}
            >
              <Tab
                icon={<PersonIcon />}
                label="账号密码登录"
                iconPosition="start"
              />
              <Tab
                icon={<PhoneIcon />}
                label="手机验证码登录"
                iconPosition="start"
              />
            </Tabs>

            {error && (
              <Alert
                severity="error"
                sx={{ mb: 3 }}
                variant="filled"
              >
                {error}
              </Alert>
            )}

            {tabValue === 0 ? (
              // 账号密码登录
              <Box component="form" onSubmit={handleSubmit}>
                <TextField
                  label={zhTranslations.auth?.login?.usernameLabel || '用户名或邮箱'}
                  variant="outlined"
                  fullWidth
                  margin="normal"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  sx={{ mb: 2 }}
                  disabled={loading}
                />

                <TextField
                  label={zhTranslations.auth?.login?.passwordLabel || '密码'}
                  type={showPassword ? 'text' : 'password'}
                  variant="outlined"
                  fullWidth
                  margin="normal"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                          disabled={loading}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 3 }}
                />

                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  size="large"
                  disabled={loading}
                  startIcon={<LoginIcon />}
                  sx={{
                    py: 1.5,
                    mb: 2,
                    background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                    boxShadow: '0 4px 20px rgba(0, 180, 216, 0.4)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #0096c7 30%, #48cae4 90%)',
                      transform: 'translateY(-2px)',
                      boxShadow: '0 6px 25px rgba(0, 180, 216, 0.5)',
                    },
                  }}
                >
                  {loading ? '登录中...' : (zhTranslations.auth?.login?.submitButton || '登录')}
                </Button>
              </Box>
            ) : (
              // 手机验证码登录
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  使用已绑定的手机号码登录，无需输入密码。
                </Typography>
                <PhoneVerification onVerified={handlePhoneVerified} />
              </Box>
            )}

            <Divider sx={{ my: 3 }} />

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2">
                {zhTranslations.auth?.login?.noAccount || '还没有账号？'}{' '}
                <Link
                  component={RouterLink}
                  to="/register"
                  sx={{
                    fontWeight: 600,
                    color: 'primary.main',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline',
                    }
                  }}
                >
                  {zhTranslations.auth?.login?.registerLink || '立即注册'}
                </Link>
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Fade>
  );
};

export default Login;
