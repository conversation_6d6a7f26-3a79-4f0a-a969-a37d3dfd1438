import { useState } from 'react';
import { 
  <PERSON>, 
  Card, 
  CardContent, 
  Typo<PERSON>, 
  Button, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  Fade
} from '@mui/material';
import { 
  Login as LoginIcon, 
  PersonAdd as PersonAddIcon,
  Lock as LockIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface LoginPromptProps {
  message?: string;
  redirectPath?: string;
}

/**
 * 登录提示组件
 * 当用户尝试访问需要登录的页面时显示
 */
const LoginPrompt = ({ 
  message = '请登录后访问此页面', 
  redirectPath 
}: LoginPromptProps) => {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  
  const handleLogin = () => {
    navigate('/login', { state: { from: redirectPath || window.location.pathname } });
  };
  
  const handleRegister = () => {
    navigate('/register');
  };
  
  const handleOpenDialog = () => {
    setO<PERSON>(true);
  };
  
  const handleCloseDialog = () => {
    setOpen(false);
  };
  
  return (
    <Fade in={true} timeout={800}>
      <Box sx={{ 
        maxWidth: 600, 
        mx: 'auto', 
        mt: 8,
        px: 2,
        textAlign: 'center'
      }}>
        <Card sx={{ 
          borderRadius: 3,
          boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
          position: 'relative',
          overflow: 'visible',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: '-10px',
            left: '-10px',
            right: '-10px',
            bottom: '-10px',
            background: 'linear-gradient(135deg, rgba(0, 180, 216, 0.2) 0%, rgba(144, 224, 239, 0.1) 100%)',
            borderRadius: '20px',
            filter: 'blur(20px)',
            opacity: 0.7,
            zIndex: -1,
          },
        }}>
          <CardContent sx={{ p: 4 }}>
            <LockIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
            
            <Typography 
              variant="h4" 
              gutterBottom
              sx={{ 
                color: 'primary.main',
                fontWeight: 700,
                mb: 2
              }}
            >
              需要登录
            </Typography>
            
            <Typography 
              variant="body1" 
              sx={{ mb: 4, maxWidth: 450, mx: 'auto' }}
            >
              {message}
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="large"
                startIcon={<LoginIcon />}
                onClick={handleLogin}
                sx={{ 
                  py: 1.5,
                  px: 3,
                  background: 'linear-gradient(45deg, #00b4d8 30%, #90e0ef 90%)',
                  boxShadow: '0 4px 20px rgba(0, 180, 216, 0.4)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #0096c7 30%, #48cae4 90%)',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 25px rgba(0, 180, 216, 0.5)',
                  },
                }}
              >
                登录
              </Button>
              
              <Button
                variant="outlined"
                size="large"
                startIcon={<PersonAddIcon />}
                onClick={handleRegister}
                sx={{ 
                  py: 1.5,
                  px: 3,
                  borderWidth: '2px',
                  '&:hover': {
                    borderWidth: '2px',
                    transform: 'translateY(-2px)',
                  }
                }}
              >
                注册新账号
              </Button>
            </Box>
            
            <Button
              variant="text"
              onClick={handleOpenDialog}
              sx={{ mt: 3 }}
            >
              为什么需要登录?
            </Button>
            
            <Dialog open={open} onClose={handleCloseDialog}>
              <DialogTitle>为什么需要登录?</DialogTitle>
              <DialogContent>
                <Typography paragraph>
                  登录后，您可以享受以下功能和好处：
                </Typography>
                <Typography component="div">
                  <ul>
                    <li>保存您的简历和优化历史</li>
                    <li>获取个性化的职位推荐</li>
                    <li>查看和分享面试经验</li>
                    <li>收藏感兴趣的面试题和职位</li>
                    <li>参与社区讨论和评论</li>
                    <li>获取更多专业的求职辅导</li>
                  </ul>
                </Typography>
                <Typography>
                  我们致力于保护您的隐私和数据安全。您的信息将仅用于提供更好的服务体验。
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={handleCloseDialog}>关闭</Button>
                <Button onClick={handleLogin} variant="contained">
                  去登录
                </Button>
              </DialogActions>
            </Dialog>
          </CardContent>
        </Card>
      </Box>
    </Fade>
  );
};

export default LoginPrompt;
