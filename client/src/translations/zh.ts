// Chinese translations
export const zhTranslations = {
  // Navigation
  appTitle: 'AI 职业助手',
  navItems: {
    resumeOptimizer: '简历优化',
    interviewExperiences: '面试经验',
    jobRecommendations: '职位推荐',
    interviewCoaching: '面试辅导'
  },

  // Authentication
  auth: {
    login: {
      title: '用户登录',
      usernameLabel: '用户名或邮箱',
      passwordLabel: '密码',
      submitButton: '登录',
      noAccount: '还没有账号？',
      registerLink: '立即注册',
      forgotPassword: '忘记密码？'
    },
    register: {
      title: '用户注册',
      usernameLabel: '用户名',
      emailLabel: '电子邮箱',
      passwordLabel: '密码',
      confirmPasswordLabel: '确认密码',
      fullNameLabel: '姓名（可选）',
      submitButton: '注册',
      hasAccount: '已有账号？',
      loginLink: '立即登录',
      termsAndConditions: '注册即表示您同意我们的服务条款和隐私政策'
    },
    profile: {
      title: '个人资料',
      editButton: '编辑',
      saveButton: '保存',
      fullNameLabel: '姓名',
      bioLabel: '个人简介',
      avatarLabel: '头像URL',
      myExperiences: '我的面经',
      myBookmarks: '我的收藏',
      logoutButton: '退出登录'
    }
  },

  // Resume Optimizer
  resumeOptimizer: {
    title: 'AI 简历优化器',
    subtitle: '使用 AI 驱动的建议优化您的简历',
    uploadResume: '上传简历',
    chooseFile: '选择文件',
    selected: '已选择：',
    optimizeResume: '优化简历',
    clear: '清除',
    analyzing: '正在分析您的简历...',
    optimizedVersion: '优化版本',
    noFileSelected: '请先上传简历文件',
    failedToOptimize: '简历优化失败',
    anErrorOccurred: '发生错误'
  },

  // Interview Experiences
  interviewExperiences: {
    title: '公司面试经验',
    subtitle: '从他人的面试历程中学习',
    mockData: {
      companies: ['科技创新公司', '数据分析有限公司', '云解决方案'],
      positions: ['高级软件工程师', '数据科学家', 'DevOps 工程师'],
      dates: ['2023年6月', '2023年4月', '2023年5月'],
      contents: [
        '面试过程包括5轮：初步筛选、技术评估、系统设计讨论、行为面试和最终团队匹配。技术问题主要集中在分布式系统和算法优化上。',
        '三阶段面试，包括一个分析数据集的家庭作业，然后是发现结果的演示和与团队的技术深入讨论。他们强调了ML模型和数据可视化的实践经验。',
        '面试主要集中在CI/CD管道、Kubernetes专业知识和云基础设施管理上。有一个实际练习来排除故障的部署管道。'
      ],
      tags: [
        ['软件工程', '算法', '系统设计'],
        ['数据科学', '机器学习', 'Python'],
        ['DevOps', 'Kubernetes', 'AWS']
      ]
    }
  },

  // Job Recommendations
  jobRecommendations: {
    title: '职位推荐',
    subtitle: '基于您的简历的个性化职位匹配',
    searchPlaceholder: '按职位、公司或技能搜索职位...',
    applyNow: '立即申请',
    mockData: {
      titles: ['高级前端开发工程师', '数据科学家', 'DevOps 工程师'],
      companies: ['科技创新公司', '数据分析有限公司', '云解决方案'],
      locations: ['旧金山，加利福尼亚（远程）', '纽约，纽约（混合）', '奥斯汀，德克萨斯（现场）'],
      salaries: ['$120,000 - $150,000', '$130,000 - $160,000', '$110,000 - $140,000'],
      descriptions: [
        '我们正在寻找一位在React、TypeScript和现代Web技术方面有专业知识的高级前端开发人员加入我们不断壮大的团队。',
        '加入我们的数据科学团队，构建机器学习模型并从大型数据集中提取见解，以推动业务决策。',
        '寻找DevOps工程师帮助我们构建和维护我们的云基础设施和CI/CD管道。'
      ],
      skills: [
        ['React', 'TypeScript', 'Redux', 'Material UI'],
        ['Python', '机器学习', 'SQL', '数据可视化'],
        ['Kubernetes', 'AWS', 'Docker', 'Terraform']
      ]
    }
  },

  // Interview Coaching
  interviewCoaching: {
    title: '真实面试辅导',
    subtitle: '与行业专家联系，获取个性化面试准备',
    whyChoose: '为什么选择面试辅导？',
    benefits: [
      {
        title: '个性化反馈',
        description: '获取针对您的技能、经验和目标职位的量身定制的建议'
      },
      {
        title: '模拟面试',
        description: '与了解公司需求的真正行业专业人士一起练习'
      },
      {
        title: '内部知识',
        description: '了解公司特定的面试技巧和期望'
      },
      {
        title: '建立信心',
        description: '培养有效展示您技能的信心'
      }
    ],
    ourExpertCoaches: '我们的专家教练',
    specialties: '专长：',
    experience: '经验',
    reviews: '评论',
    nextAvailable: '下一个可用时间：',
    bookSession: '预约会话',
    readyToAce: '准备好在下一次面试中取得好成绩？',
    coachesHelpedThousands: '我们的教练已经帮助数千名候选人在顶级公司获得他们梦想的工作。今天预约一个会话，将您的面试技巧提升到一个新的水平。',
    browseAllCoaches: '浏览所有教练',
    mockData: {
      names: ['莎拉·约翰逊', '陈明', '普里亚·帕特尔'],
      titles: ['高级技术招聘人员', '工程经理', '职业教练'],
      companies: ['科技创新公司', '数据分析有限公司', '职业加速器'],
      experiences: ['8+年', '12+年', '6+年'],
      specialties: [
        ['技术面试', '简历审查', '职业指导'],
        ['系统设计', '领导力', '技术评估'],
        ['行为面试', '薪资谈判', '个人品牌']
      ],
      availabilities: ['下一个可用时间：明天，下午2:00', '下一个可用时间：周五，上午10:00', '下一个可用时间：今天，下午5:30']
    }
  },

  // Common
  common: {
    today: '今天',
    tomorrow: '明天'
  }
};
