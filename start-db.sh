#!/bin/bash

# 确保脚本在出错时停止执行
set -e

echo "启动数据库服务..."
docker-compose up -d postgres

# 等待数据库启动
echo "等待数据库启动..."
sleep 5

echo "启动Redis服务..."
docker-compose up -d redis

# 等待Redis启动
echo "等待Redis启动..."
sleep 2

echo "服务已启动！"
echo "PostgreSQL 运行在 localhost:5432"
echo "用户名: postgres"
echo "密码: postgres"
echo "数据库名: ai_resume_optimizer"
echo "Redis 运行在 localhost:6379"

# 如果需要启动 pgAdmin
if [ "$1" = "--with-pgadmin" ]; then
  echo "启动 pgAdmin..."
  docker-compose up -d pgadmin
  echo "pgAdmin 运行在 http://localhost:5050"
  echo "登录邮箱: <EMAIL>"
  echo "登录密码: admin"
fi

echo ""
echo "要停止数据库服务，请运行: docker-compose down"
