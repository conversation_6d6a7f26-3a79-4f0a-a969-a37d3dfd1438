const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 30]
      },
      comment: '用户名'
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      },
      comment: '电子邮箱'
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
      validate: {
        is: /^1[3-9]\d{9}$/
      },
      comment: '手机号码'
    },
    phoneVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '手机号是否已验证'
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '密码（加密存储）'
    },
    fullName: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '全名'
    },
    avatar: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '头像URL'
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '个人简介'
    },
    role: {
      type: DataTypes.ENUM('user', 'admin'),
      allowNull: false,
      defaultValue: 'user',
      comment: '用户角色'
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后登录时间'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '账户是否激活'
    },
    resetPasswordToken: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '重置密码令牌'
    },
    resetPasswordExpires: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '重置密码令牌过期时间'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'users',
    timestamps: true,
    hooks: {
      // 在创建或更新用户之前加密密码
      beforeCreate: async (user) => {
        if (user.password) {
          const salt = await bcrypt.genSalt(10);
          user.password = await bcrypt.hash(user.password, salt);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('password')) {
          const salt = await bcrypt.genSalt(10);
          user.password = await bcrypt.hash(user.password, salt);
        }
      }
    }
  });

  // 实例方法：验证密码
  User.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.password);
  };

  return User;
};
