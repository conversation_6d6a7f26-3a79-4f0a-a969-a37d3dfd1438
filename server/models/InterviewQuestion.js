const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InterviewQuestion = sequelize.define('InterviewQuestion', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    question: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '面试问题内容'
    },
    answer: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '参考答案'
    },
    difficulty: {
      type: DataTypes.ENUM('简单', '中等', '困难'),
      allowNull: false,
      defaultValue: '中等',
      comment: '问题难度'
    },
    category: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '问题类别，如：技术、行为、算法等'
    },
    tags: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      defaultValue: [],
      comment: '相关标签，如：JavaScript、React、数据结构等'
    },
    company: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '关联公司，如果是特定公司的面试题'
    },
    position: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '关联职位，如果是特定职位的面试题'
    },
    views: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '浏览次数'
    },
    likes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '点赞数'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'interview_questions',
    timestamps: true,
    indexes: [
      {
        name: 'idx_interview_questions_category',
        fields: ['category']
      },
      {
        name: 'idx_interview_questions_difficulty',
        fields: ['difficulty']
      },
      {
        name: 'idx_interview_questions_company',
        fields: ['company']
      },
      {
        name: 'idx_interview_questions_position',
        fields: ['position']
      }
    ]
  });

  return InterviewQuestion;
};
