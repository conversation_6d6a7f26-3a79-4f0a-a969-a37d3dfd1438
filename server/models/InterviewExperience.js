const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const InterviewExperience = sequelize.define('InterviewExperience', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '面经标题'
    },
    company: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '公司名称'
    },
    position: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '面试职位'
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
      comment: '面试日期'
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '面经内容'
    },
    rounds: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '面试轮数'
    },
    result: {
      type: DataTypes.ENUM('通过', '未通过', '等待中', '未知'),
      allowNull: true,
      defaultValue: '未知',
      comment: '面试结果'
    },
    difficulty: {
      type: DataTypes.ENUM('简单', '中等', '困难'),
      allowNull: true,
      defaultValue: '中等',
      comment: '面试难度'
    },
    duration: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '面试时长'
    },
    tags: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      defaultValue: [],
      comment: '相关标签'
    },
    author: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '作者（可以是匿名）'
    },
    authorId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '作者ID（如果有用户系统）'
    },
    views: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '浏览次数'
    },
    likes: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '点赞数'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'interview_experiences',
    timestamps: true,
    indexes: [
      {
        name: 'idx_interview_experiences_company',
        fields: ['company']
      },
      {
        name: 'idx_interview_experiences_position',
        fields: ['position']
      },
      {
        name: 'idx_interview_experiences_date',
        fields: ['date']
      },
      {
        name: 'idx_interview_experiences_result',
        fields: ['result']
      }
    ]
  });

  return InterviewExperience;
};
