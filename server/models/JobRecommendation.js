const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const JobRecommendation = sequelize.define('JobRecommendation', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '职位标题'
    },
    company: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '公司名称'
    },
    location: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '工作地点'
    },
    salary: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '薪资范围'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '职位描述'
    },
    requirements: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '职位要求'
    },
    jobType: {
      type: DataTypes.ENUM('全职', '兼职', '实习', '合同', '自由职业'),
      allowNull: false,
      defaultValue: '全职',
      comment: '工作类型'
    },
    experienceLevel: {
      type: DataTypes.ENUM('入门级', '初级', '中级', '高级', '专家级'),
      allowNull: true,
      comment: '经验要求'
    },
    educationLevel: {
      type: DataTypes.ENUM('高中', '大专', '本科', '硕士', '博士', '不限'),
      allowNull: true,
      defaultValue: '不限',
      comment: '教育要求'
    },
    skills: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      defaultValue: [],
      comment: '所需技能'
    },
    benefits: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      defaultValue: [],
      comment: '工作福利'
    },
    applicationUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '申请链接'
    },
    companyLogo: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '公司logo URL'
    },
    featured: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否为推荐职位'
    },
    postedDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '发布日期'
    },
    expiryDate: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '截止日期'
    },
    views: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '浏览次数'
    },
    applications: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '申请人数'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'job_recommendations',
    timestamps: true,
    indexes: [
      {
        name: 'idx_job_recommendations_title',
        fields: ['title']
      },
      {
        name: 'idx_job_recommendations_company',
        fields: ['company']
      },
      {
        name: 'idx_job_recommendations_location',
        fields: ['location']
      },
      {
        name: 'idx_job_recommendations_job_type',
        fields: ['jobType']
      },
      {
        name: 'idx_job_recommendations_featured',
        fields: ['featured']
      },
      {
        name: 'idx_job_recommendations_posted_date',
        fields: ['postedDate']
      }
    ]
  });

  return JobRecommendation;
};
