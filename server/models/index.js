const { Sequelize } = require('sequelize');
const dotenv = require('dotenv');

dotenv.config();

// 从环境变量中获取数据库连接信息
const DB_NAME = process.env.DB_NAME || 'ai_resume_optimizer';
const DB_USER = process.env.DB_USER || 'postgres';
const DB_PASSWORD = process.env.DB_PASSWORD || 'postgres';
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || 5432;

// 创建 Sequelize 实例
const sequelize = new Sequelize(DB_NAME, DB_USER, DB_PASSWORD, {
  host: DB_HOST,
  port: DB_PORT,
  dialect: 'postgres',
  logging: false, // 设置为 true 可以在控制台看到 SQL 查询
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  // 重试连接配置，适用于 Docker 环境中数据库可能需要时间启动的情况
  retry: {
    max: 5,
    match: [
      /SequelizeConnectionRefusedError/,
      /SequelizeConnectionError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/
    ],
    backoffBase: 1000,
    backoffExponent: 1.5,
  }
});

// 测试数据库连接
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
}

// 导入模型
const User = require('./User')(sequelize);
const InterviewQuestion = require('./InterviewQuestion')(sequelize);
const InterviewExperience = require('./InterviewExperience')(sequelize);
const JobRecommendation = require('./JobRecommendation')(sequelize);

// 设置模型关联
// 用户与面经的关联
User.hasMany(InterviewExperience, { foreignKey: 'authorId', as: 'experiences' });
InterviewExperience.belongsTo(User, { foreignKey: 'authorId', as: 'user' });

// 同步所有模型
async function syncModels() {
  try {
    // force: true 会在每次启动应用时删除并重新创建表（仅在开发环境使用）
    // 生产环境应该设置为 false 或使用迁移
    await sequelize.sync({ force: process.env.NODE_ENV === 'development' });
    console.log('所有模型已同步');
  } catch (error) {
    console.error('模型同步失败:', error);
  }
}

module.exports = {
  sequelize,
  testConnection,
  syncModels,
  User,
  InterviewQuestion,
  InterviewExperience,
  JobRecommendation
};
