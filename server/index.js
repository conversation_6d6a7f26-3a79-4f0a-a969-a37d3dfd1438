const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const pdf = require('pdf-parse');
const mammoth = require('mammoth');
const axios = require('axios');
const { testConnection } = require('./models');
const { authenticate } = require('./middleware/auth');

dotenv.config();

const app = express();
const port = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

// 测试数据库连接
testConnection();

const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
      cb(null, Date.now() + path.extname(file.originalname));
    }
  }),
  fileFilter: (req, file, cb) => {
    const filetypes = /pdf|doc|docx/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Only PDF, DOC, and DOCX files are allowed!'));
    }
  }
});

// Create uploads directory if it doesn't exist
if (!fs.existsSync('uploads')) {
  fs.mkdirSync('uploads');
}

async function extractTextFromFile(filePath, fileType) {
  try {
    if (fileType === 'pdf') {
      const dataBuffer = fs.readFileSync(filePath);
      const data = await pdf(dataBuffer);
      return data.text;
    } else if (fileType === 'doc' || fileType === 'docx') {
      const result = await mammoth.extractRawText({ path: filePath });
      return result.value;
    }
  } catch (error) {
    console.error('Error extracting text:', error);
    throw new Error('Failed to extract text from file');
  }
}

app.post('/api/optimize-resume', authenticate, upload.single('resume'), async (req, res) => {
  try {
    let resumeText = '';

    if (req.file) {
      const fileType = path.extname(req.file.originalname).substring(1);
      resumeText = await extractTextFromFile(req.file.path, fileType);

      // Clean up the uploaded file
      fs.unlinkSync(req.file.path);
    }

    if (!resumeText) {
      return res.status(400).json({ error: '未提供简历内容' });
    }

    // 调用自定义模型 API
    const response = await axios.post(process.env.MODEL_API_URL, {
      model: "deepseek-ai/DeepSeek-V3",
      messages: [
        {
          role: "system",
          content: "你是一位专业的简历优化专家。请分析简历并提供反馈。你必须只返回一个有效的JSON对象，不要包含任何markdown格式或额外文本。JSON对象必须遵循以下结构：{\"summary\": string, \"sections\": [{\"title\": string, \"suggestions\": string[]}], \"improvedVersion\": string}。sections必须包含：'个人履历与技能描述'、'项目经历'、'行业推荐'。每个建议都应该具体且可操作。确保所有字符串值都正确转义，响应是可解析的JSON字符串。请用中文回复。"
        },
        {
          role: "user",
          content: `请分析并优化以下简历：\n\n${resumeText}`
        }
      ]
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.MODEL_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    // 解析响应内容
    try {
      const content = response.data.choices[0].message.content;
      // 如果返回的内容包含JSON代码块，提取其中的JSON
      const jsonMatch = content.match(/```json\s*({[\s\S]*?})\s*```/) || content.match(/({[\s\S]*})/);
      const jsonContent = jsonMatch ? jsonMatch[1] : content;

      // 尝试解析JSON
      const optimizedResume = JSON.parse(jsonContent.trim());

      res.json(optimizedResume);
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      res.status(500).json({ error: '无法解析模型响应' });
    }
  } catch (error) {
    console.error('错误:', error);
    res.status(500).json({ error: '简历优化失败' });
  }
});

// 导入路由
const authRouter = require('./routes/auth');
const smsRouter = require('./routes/sms');
const interviewQuestionsRouter = require('./routes/interviewQuestions');
const interviewExperiencesRouter = require('./routes/interviewExperiences');
const jobRecommendationsRouter = require('./routes/jobRecommendations');

// 使用路由
app.use('/api/auth', authRouter);
app.use('/api/sms', smsRouter);
app.use('/api/interview-questions', interviewQuestionsRouter);
app.use('/api/interview-experiences', interviewExperiencesRouter);
app.use('/api/job-recommendations', jobRecommendationsRouter);

// 初始化数据库种子数据的路由（仅在开发环境使用）
if (process.env.NODE_ENV === 'development') {
  app.post('/api/seed-database', async (req, res) => {
    try {
      const seedDatabase = require('./seeders/seed');
      await seedDatabase();
      res.json({ success: true, message: '数据库初始化成功' });
    } catch (error) {
      console.error('数据库初始化失败:', error);
      res.status(500).json({ error: '数据库初始化失败' });
    }
  });
}

app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});
