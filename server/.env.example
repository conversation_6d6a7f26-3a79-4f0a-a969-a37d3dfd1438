# 服务器配置
PORT=3001
NODE_ENV=development

# 数据库配置
DB_NAME=ai_resume_optimizer
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=localhost  # 使用 Docker 时可以设置为 'postgres'
DB_PORT=5432

# pgAdmin 配置 (可选)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin
PGADMIN_PORT=5050

# API 配置
MODEL_API_URL=https://api.deepseek.com/v1/chat/completions
MODEL_API_KEY=your_api_key_here

# JWT 配置
JWT_SECRET=your_jwt_secret_key_change_this_in_production
JWT_EXPIRES_IN=7d

# 阿里云短信服务配置
ALIYUN_ACCESS_KEY_ID=your_aliyun_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
ALIYUN_SMS_SIGN_NAME=your_sign_name
ALIYUN_SMS_TEMPLATE_CODE=your_template_code

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 验证码配置
SMS_CODE_EXPIRE=300  # 验证码有效期（秒）
SMS_SEND_INTERVAL=60  # 同一手机号发送间隔（秒）
SMS_DAY_LIMIT=10      # 同一手机号每日发送上限
SMS_IP_MINUTE_LIMIT=5 # 同一IP每分钟发送上限
