const {
  User,
  InterviewQuestion,
  InterviewExperience,
  JobRecommendation,
  sequelize,
  syncModels
} = require('../models');

// 用户种子数据
const users = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    fullName: '管理员',
    role: 'admin',
    isActive: true
  },
  {
    username: 'user1',
    email: '<EMAIL>',
    password: 'password123',
    fullName: '张三',
    bio: '前端开发工程师，热爱React和TypeScript',
    role: 'user',
    isActive: true
  },
  {
    username: 'user2',
    email: '<EMAIL>',
    password: 'password123',
    fullName: '李四',
    bio: '后端开发工程师，专注于Java和Spring Boot',
    role: 'user',
    isActive: true
  }
];

// 面试题种子数据
const interviewQuestions = [
  {
    question: '请解释React中的虚拟DOM及其工作原理',
    answer: '虚拟DOM是React的一个核心概念，它是真实DOM的一个轻量级JavaScript表示。当组件状态改变时，React首先在虚拟DOM中进行更新，然后通过差异算法(diffing)比较新旧虚拟DOM树的差异，最后只将必要的更改应用到实际DOM上，从而提高性能。这种方法避免了直接操作DOM，减少了重排和重绘，提高了应用性能。',
    difficulty: '中等',
    category: '前端',
    tags: ['React', 'JavaScript', '前端框架'],
    company: '科技创新公司',
    position: '前端开发工程师'
  },
  {
    question: '什么是闭包(Closure)？请给出一个实际应用的例子',
    answer: '闭包是指一个函数能够访问并记住其词法作用域，即使该函数在其词法作用域之外执行。实际应用例子：创建私有变量。例如：function createCounter() { let count = 0; return function() { return ++count; }; } const counter = createCounter(); counter(); // 1, counter(); // 2。这里内部函数形成了一个闭包，可以访问外部函数的count变量，而外部无法直接访问这个变量。',
    difficulty: '中等',
    category: '前端',
    tags: ['JavaScript', '函数式编程'],
    company: '数据分析有限公司',
    position: '全栈开发工程师'
  },
  {
    question: '请解释HTTP状态码401和403的区别',
    answer: '401 Unauthorized：表示请求未经授权，客户端需要提供身份验证信息（如用户名和密码）。这通常意味着用户尚未登录或提供的凭据无效。403 Forbidden：表示服务器理解请求但拒绝执行，即使提供了身份验证也不会允许访问。这通常意味着用户已经登录，但没有足够的权限访问请求的资源。',
    difficulty: '简单',
    category: '网络',
    tags: ['HTTP', 'Web开发', '安全'],
    company: '云解决方案',
    position: 'DevOps工程师'
  },
  {
    question: '如何设计一个高并发的系统？',
    answer: '设计高并发系统需要考虑多个方面：1. 水平扩展：使用负载均衡器分发请求到多台服务器。2. 缓存策略：使用Redis等缓存热点数据，减少数据库访问。3. 数据库优化：使用读写分离、分库分表、索引优化等策略。4. 异步处理：使用消息队列处理非实时任务，减轻主系统压力。5. CDN加速：使用CDN分发静态资源。6. 限流熔断：实施限流和熔断机制，防止系统过载。7. 监控和预警：实时监控系统性能，及时发现问题。',
    difficulty: '困难',
    category: '系统设计',
    tags: ['架构', '高并发', '分布式系统'],
    company: '科技创新公司',
    position: '高级后端工程师'
  },
  {
    question: '请解释机器学习中过拟合(Overfitting)的概念及如何避免',
    answer: '过拟合是指模型在训练数据上表现很好，但在新数据上表现较差的现象，即模型学习了训练数据中的噪声和随机波动，而不是底层模式。避免过拟合的方法包括：1. 增加训练数据量。2. 使用正则化技术（如L1、L2正则化）。3. 简化模型复杂度。4. 使用交叉验证。5. 早停法(Early Stopping)。6. 集成学习方法。7. 特征选择和降维。8. 使用Dropout等技术（在神经网络中）。',
    difficulty: '中等',
    category: '数据科学',
    tags: ['机器学习', '数据科学', '算法'],
    company: '数据分析有限公司',
    position: '数据科学家'
  }
];

// 面经种子数据
const interviewExperiences = [
  {
    title: '科技创新公司高级前端开发面试经历',
    company: '科技创新公司',
    position: '高级前端开发工程师',
    date: new Date('2023-06-15'),
    content: '面试过程包括5轮：初步筛选、技术评估、系统设计讨论、行为面试和最终团队匹配。技术问题主要集中在React深入原理、性能优化和大规模应用架构设计。系统设计环节要求设计一个实时协作编辑器，考察了WebSocket和状态同步的理解。行为面试关注团队协作和解决冲突的能力。整体面试难度中上，但氛围友好。最后一轮与团队成员交流，主要是文化匹配和项目经验分享。',
    rounds: 5,
    result: '通过',
    difficulty: '中等',
    duration: '2周',
    tags: ['前端', 'React', '系统设计'],
    author: '张三',
    authorId: 2, // 对应 user1
    views: 1250,
    likes: 78
  },
  {
    title: '数据分析有限公司数据科学家面试分享',
    company: '数据分析有限公司',
    position: '数据科学家',
    date: new Date('2023-04-20'),
    content: '三阶段面试，包括一个分析数据集的家庭作业，然后是发现结果的演示和与团队的技术深入讨论。家庭作业是分析一个电商数据集，找出用户行为模式和购买预测因素。演示环节需要展示数据可视化和模型解释能力。技术讨论涉及特征工程、模型选择和评估方法。他们强调了ML模型和数据可视化的实践经验，以及向非技术人员解释复杂分析的能力。整个过程注重实际问题解决而非纯理论。',
    rounds: 3,
    result: '通过',
    difficulty: '困难',
    duration: '3周',
    tags: ['数据科学', '机器学习', 'Python'],
    author: '李四',
    authorId: 3, // 对应 user2
    views: 980,
    likes: 65
  },
  {
    title: '云解决方案DevOps工程师面试经验',
    company: '云解决方案',
    position: 'DevOps工程师',
    date: new Date('2023-05-10'),
    content: '面试主要集中在CI/CD管道、Kubernetes专业知识和云基础设施管理上。第一轮是技术筛选，基础知识问答。第二轮是实际练习，需要排除故障的部署管道和优化Kubernetes集群配置。第三轮是架构讨论，设计一个多环境、多区域的部署策略。最后一轮是与团队主管的文化匹配面试。整个过程非常注重实际操作能力和问题解决思路，而不仅仅是理论知识。公司文化强调自主性和持续学习。',
    rounds: 4,
    result: '通过',
    difficulty: '中等',
    duration: '10天',
    tags: ['DevOps', 'Kubernetes', 'AWS'],
    author: '管理员',
    authorId: 1, // 对应 admin
    views: 850,
    likes: 42
  }
];

// 推荐岗位种子数据
const jobRecommendations = [
  {
    title: '高级前端开发工程师',
    company: '科技创新公司',
    location: '北京（可远程）',
    salary: '30K-50K',
    description: '我们正在寻找一位在React、TypeScript和现代Web技术方面有专业知识的高级前端开发人员加入我们不断壮大的团队。您将负责构建高性能、可扩展的用户界面，并与后端团队协作开发完整的功能。',
    requirements: '- 5年以上前端开发经验\n- 精通React、TypeScript和现代JavaScript\n- 熟悉状态管理（Redux、MobX等）\n- 了解前端性能优化技术\n- 良好的团队协作和沟通能力',
    jobType: '全职',
    experienceLevel: '高级',
    educationLevel: '本科',
    skills: ['React', 'TypeScript', 'Redux', 'Webpack', 'CSS3'],
    benefits: ['弹性工作时间', '股票期权', '年度旅游', '持续学习基金'],
    applicationUrl: 'https://example.com/apply',
    companyLogo: 'https://randomuser.me/api/portraits/men/1.jpg',
    featured: true,
    postedDate: new Date('2023-06-01'),
    expiryDate: new Date('2023-08-01'),
    views: 1560,
    applications: 78
  },
  {
    title: '数据科学家',
    company: '数据分析有限公司',
    location: '上海（混合办公）',
    salary: '35K-60K',
    description: '加入我们的数据科学团队，构建机器学习模型并从大型数据集中提取见解，以推动业务决策。您将与产品和工程团队密切合作，将数据驱动的解决方案集成到我们的产品中。',
    requirements: '- 3年以上数据科学或相关领域经验\n- 精通Python和数据分析库（Pandas、NumPy等）\n- 熟悉机器学习框架（Scikit-learn、TensorFlow或PyTorch）\n- 良好的数据可视化和沟通能力\n- 统计学或计算机科学相关学位',
    jobType: '全职',
    experienceLevel: '中级',
    educationLevel: '硕士',
    skills: ['Python', '机器学习', 'SQL', '数据可视化', '统计分析'],
    benefits: ['灵活工作安排', '健康保险', '年度奖金', '专业发展机会'],
    applicationUrl: 'https://example.com/apply',
    companyLogo: 'https://randomuser.me/api/portraits/women/1.jpg',
    featured: true,
    postedDate: new Date('2023-05-15'),
    expiryDate: new Date('2023-07-15'),
    views: 1280,
    applications: 65
  },
  {
    title: 'DevOps工程师',
    company: '云解决方案',
    location: '深圳（现场办公）',
    salary: '25K-45K',
    description: '寻找DevOps工程师帮助我们构建和维护我们的云基础设施和CI/CD管道。您将负责自动化部署流程，确保系统的可靠性和安全性，并与开发团队合作优化开发工作流程。',
    requirements: '- 3年以上DevOps或SRE经验\n- 熟悉容器技术（Docker、Kubernetes）\n- 精通至少一种云平台（AWS、Azure或GCP）\n- CI/CD工具经验（Jenkins、GitLab CI等）\n- 基础设施即代码经验（Terraform、Ansible等）',
    jobType: '全职',
    experienceLevel: '中级',
    educationLevel: '本科',
    skills: ['Kubernetes', 'AWS', 'Docker', 'Terraform', 'CI/CD'],
    benefits: ['五险一金', '年终奖', '技术分享会', '定期团建'],
    applicationUrl: 'https://example.com/apply',
    companyLogo: 'https://randomuser.me/api/portraits/men/2.jpg',
    featured: false,
    postedDate: new Date('2023-05-20'),
    expiryDate: new Date('2023-07-20'),
    views: 980,
    applications: 42
  }
];

// 初始化数据库
async function seedDatabase() {
  try {
    // 同步模型（创建表）
    await syncModels();

    // 批量创建记录
    await User.bulkCreate(users);
    console.log('用户数据已添加');

    await InterviewQuestion.bulkCreate(interviewQuestions);
    console.log('面试题数据已添加');

    await InterviewExperience.bulkCreate(interviewExperiences);
    console.log('面经数据已添加');

    await JobRecommendation.bulkCreate(jobRecommendations);
    console.log('推荐岗位数据已添加');

    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 如果直接运行此文件，则执行种子函数
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;
