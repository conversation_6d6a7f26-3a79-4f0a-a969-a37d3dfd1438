const express = require('express');
const router = express.Router();
const { InterviewQuestion } = require('../models');
const { Op } = require('sequelize');
const { authenticate, optionalAuth } = require('../middleware/auth');

// 获取所有面试题
router.get('/', authenticate, async (req, res) => {
  try {
    const {
      category,
      difficulty,
      company,
      position,
      tag,
      search,
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'DESC'
    } = req.query;

    // 构建查询条件
    const where = {};
    if (category) where.category = category;
    if (difficulty) where.difficulty = difficulty;
    if (company) where.company = company;
    if (position) where.position = position;
    if (tag) where.tags = { [Op.contains]: [tag] };
    if (search) {
      where[Op.or] = [
        { question: { [Op.iLike]: `%${search}%` } },
        { answer: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 分页和排序
    const offset = (page - 1) * limit;
    const orderClause = [[sort, order]];

    // 查询数据
    const { count, rows } = await InterviewQuestion.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset,
      order: orderClause
    });

    // 返回结果
    res.json({
      total: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      data: rows
    });
  } catch (error) {
    console.error('获取面试题失败:', error);
    res.status(500).json({ error: '获取面试题失败' });
  }
});

// 获取单个面试题
router.get('/:id', authenticate, async (req, res) => {
  try {
    const question = await InterviewQuestion.findByPk(req.params.id);
    if (!question) {
      return res.status(404).json({ error: '面试题不存在' });
    }

    // 更新浏览次数
    await question.update({ views: question.views + 1 });

    res.json(question);
  } catch (error) {
    console.error('获取面试题失败:', error);
    res.status(500).json({ error: '获取面试题失败' });
  }
});

// 创建面试题
router.post('/', async (req, res) => {
  try {
    const question = await InterviewQuestion.create(req.body);
    res.status(201).json(question);
  } catch (error) {
    console.error('创建面试题失败:', error);
    res.status(500).json({ error: '创建面试题失败' });
  }
});

// 更新面试题
router.put('/:id', async (req, res) => {
  try {
    const question = await InterviewQuestion.findByPk(req.params.id);
    if (!question) {
      return res.status(404).json({ error: '面试题不存在' });
    }

    await question.update(req.body);
    res.json(question);
  } catch (error) {
    console.error('更新面试题失败:', error);
    res.status(500).json({ error: '更新面试题失败' });
  }
});

// 删除面试题
router.delete('/:id', async (req, res) => {
  try {
    const question = await InterviewQuestion.findByPk(req.params.id);
    if (!question) {
      return res.status(404).json({ error: '面试题不存在' });
    }

    await question.destroy();
    res.status(204).end();
  } catch (error) {
    console.error('删除面试题失败:', error);
    res.status(500).json({ error: '删除面试题失败' });
  }
});

// 点赞面试题
router.post('/:id/like', authenticate, async (req, res) => {
  try {
    const question = await InterviewQuestion.findByPk(req.params.id);
    if (!question) {
      return res.status(404).json({ error: '面试题不存在' });
    }

    await question.update({ likes: question.likes + 1 });
    res.json({ likes: question.likes + 1 });
  } catch (error) {
    console.error('点赞面试题失败:', error);
    res.status(500).json({ error: '点赞面试题失败' });
  }
});

// 获取面试题类别列表
router.get('/categories/list', authenticate, async (req, res) => {
  try {
    const categories = await InterviewQuestion.findAll({
      attributes: ['category'],
      group: ['category']
    });

    res.json(categories.map(item => item.category));
  } catch (error) {
    console.error('获取面试题类别失败:', error);
    res.status(500).json({ error: '获取面试题类别失败' });
  }
});

module.exports = router;
