const express = require('express');
const router = express.Router();
const { sendVerificationCode, verifyCode } = require('../utils/smsService');

/**
 * 获取客户端IP地址
 * @param {Object} req - Express请求对象
 * @returns {string} IP地址
 */
function getClientIp(req) {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         req.connection.socket.remoteAddress;
}

/**
 * 验证手机号格式
 * @param {string} phoneNumber - 手机号码
 * @returns {boolean} 是否是有效的中国手机号
 */
function isValidChinesePhoneNumber(phoneNumber) {
  // 中国大陆手机号格式验证
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(phoneNumber);
}

/**
 * 发送短信验证码
 * POST /api/sms/send
 * @param {string} phoneNumber - 手机号码
 */
router.post('/send', async (req, res) => {
  try {
    const { phoneNumber } = req.body;
    
    // 验证手机号
    if (!phoneNumber) {
      return res.status(400).json({ error: '手机号不能为空' });
    }
    
    if (!isValidChinesePhoneNumber(phoneNumber)) {
      return res.status(400).json({ error: '无效的手机号码格式' });
    }
    
    // 获取客户端IP
    const clientIp = getClientIp(req);
    
    // 发送验证码
    const result = await sendVerificationCode(phoneNumber, clientIp);
    
    if (result.success) {
      res.json({ message: result.message });
    } else {
      res.status(429).json({ error: result.message });
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    res.status(500).json({ error: '发送验证码失败' });
  }
});

/**
 * 验证短信验证码
 * POST /api/sms/verify
 * @param {string} phoneNumber - 手机号码
 * @param {string} code - 验证码
 */
router.post('/verify', async (req, res) => {
  try {
    const { phoneNumber, code } = req.body;
    
    // 验证参数
    if (!phoneNumber || !code) {
      return res.status(400).json({ error: '手机号和验证码不能为空' });
    }
    
    if (!isValidChinesePhoneNumber(phoneNumber)) {
      return res.status(400).json({ error: '无效的手机号码格式' });
    }
    
    // 验证验证码
    const isValid = await verifyCode(phoneNumber, code);
    
    if (isValid) {
      res.json({ valid: true, message: '验证码验证成功' });
    } else {
      res.status(400).json({ valid: false, error: '验证码无效或已过期' });
    }
  } catch (error) {
    console.error('验证码验证失败:', error);
    res.status(500).json({ error: '验证码验证失败' });
  }
});

module.exports = router;
