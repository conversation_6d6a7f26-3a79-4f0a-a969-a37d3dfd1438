const express = require('express');
const router = express.Router();
const { JobRecommendation } = require('../models');
const { Op } = require('sequelize');
const { authenticate, optionalAuth } = require('../middleware/auth');

// 获取所有推荐岗位
router.get('/', authenticate, async (req, res) => {
  try {
    const {
      company,
      location,
      jobType,
      experienceLevel,
      educationLevel,
      skill,
      featured,
      search,
      minSalary,
      maxSalary,
      page = 1,
      limit = 10,
      sort = 'postedDate',
      order = 'DESC'
    } = req.query;

    // 构建查询条件
    const where = {};
    if (company) where.company = company;
    if (location) where.location = { [Op.iLike]: `%${location}%` };
    if (jobType) where.jobType = jobType;
    if (experienceLevel) where.experienceLevel = experienceLevel;
    if (educationLevel) where.educationLevel = educationLevel;
    if (skill) where.skills = { [Op.contains]: [skill] };
    if (featured === 'true') where.featured = true;

    // 薪资范围过滤（这里简化处理，实际可能需要更复杂的逻辑）
    if (minSalary || maxSalary) {
      where.salary = {};
      if (minSalary) where.salary[Op.gte] = minSalary;
      if (maxSalary) where.salary[Op.lte] = maxSalary;
    }

    // 搜索条件
    if (search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { company: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
        { requirements: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 只显示未过期的岗位
    where.expiryDate = { [Op.or]: [{ [Op.gt]: new Date() }, { [Op.is]: null }] };

    // 分页和排序
    const offset = (page - 1) * limit;
    const orderClause = [[sort, order]];

    // 查询数据
    const { count, rows } = await JobRecommendation.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset,
      order: orderClause
    });

    // 返回结果
    res.json({
      total: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      data: rows
    });
  } catch (error) {
    console.error('获取推荐岗位失败:', error);
    res.status(500).json({ error: '获取推荐岗位失败' });
  }
});

// 获取单个推荐岗位
router.get('/:id', authenticate, async (req, res) => {
  try {
    const job = await JobRecommendation.findByPk(req.params.id);
    if (!job) {
      return res.status(404).json({ error: '岗位不存在' });
    }

    // 更新浏览次数
    await job.update({ views: job.views + 1 });

    res.json(job);
  } catch (error) {
    console.error('获取岗位失败:', error);
    res.status(500).json({ error: '获取岗位失败' });
  }
});

// 创建推荐岗位
router.post('/', async (req, res) => {
  try {
    const job = await JobRecommendation.create(req.body);
    res.status(201).json(job);
  } catch (error) {
    console.error('创建岗位失败:', error);
    res.status(500).json({ error: '创建岗位失败' });
  }
});

// 更新推荐岗位
router.put('/:id', async (req, res) => {
  try {
    const job = await JobRecommendation.findByPk(req.params.id);
    if (!job) {
      return res.status(404).json({ error: '岗位不存在' });
    }

    await job.update(req.body);
    res.json(job);
  } catch (error) {
    console.error('更新岗位失败:', error);
    res.status(500).json({ error: '更新岗位失败' });
  }
});

// 删除推荐岗位
router.delete('/:id', async (req, res) => {
  try {
    const job = await JobRecommendation.findByPk(req.params.id);
    if (!job) {
      return res.status(404).json({ error: '岗位不存在' });
    }

    await job.destroy();
    res.status(204).end();
  } catch (error) {
    console.error('删除岗位失败:', error);
    res.status(500).json({ error: '删除岗位失败' });
  }
});

// 申请岗位
router.post('/:id/apply', authenticate, async (req, res) => {
  try {
    const job = await JobRecommendation.findByPk(req.params.id);
    if (!job) {
      return res.status(404).json({ error: '岗位不存在' });
    }

    // 增加申请人数
    await job.update({ applications: job.applications + 1 });

    // 这里可以添加更多逻辑，如保存申请人信息等

    res.json({ success: true, message: '申请成功', applications: job.applications + 1 });
  } catch (error) {
    console.error('申请岗位失败:', error);
    res.status(500).json({ error: '申请岗位失败' });
  }
});

// 获取推荐岗位
router.get('/recommendations/for-user', authenticate, async (req, res) => {
  try {
    // 这里应该根据用户的简历和偏好来推荐岗位
    // 简化版本：返回精选岗位
    const jobs = await JobRecommendation.findAll({
      where: { featured: true },
      limit: 5,
      order: [['postedDate', 'DESC']]
    });

    res.json(jobs);
  } catch (error) {
    console.error('获取推荐岗位失败:', error);
    res.status(500).json({ error: '获取推荐岗位失败' });
  }
});

// 获取热门技能列表
router.get('/skills/popular', authenticate, async (req, res) => {
  try {
    // 这个查询在PostgreSQL中比较复杂，需要使用unnest和group by
    // 简化版本：返回预定义的热门技能
    const popularSkills = [
      'JavaScript', 'Python', 'React', 'Java', 'SQL',
      'AWS', 'Docker', 'Kubernetes', 'Machine Learning', 'Data Analysis'
    ];

    res.json(popularSkills);
  } catch (error) {
    console.error('获取热门技能失败:', error);
    res.status(500).json({ error: '获取热门技能失败' });
  }
});

module.exports = router;
