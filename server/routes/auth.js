const express = require('express');
const router = express.Router();
const { User } = require('../models');
const { generateToken, authenticate } = require('../middleware/auth');
const { verifyCode } = require('../utils/smsService');
const { Op } = require('sequelize');

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, fullName, phoneNumber, smsCode } = req.body;

    // 验证必填字段
    if (!username || !email || !password) {
      return res.status(400).json({ error: '用户名、邮箱和密码为必填项' });
    }

    // 检查用户名是否已存在
    const existingUsername = await User.findOne({ where: { username } });
    if (existingUsername) {
      return res.status(400).json({ error: '用户名已被使用' });
    }

    // 检查邮箱是否已存在
    const existingEmail = await User.findOne({ where: { email } });
    if (existingEmail) {
      return res.status(400).json({ error: '邮箱已被注册' });
    }

    // 如果提供了手机号，则验证手机号和验证码
    let phoneVerified = false;
    if (phoneNumber) {
      // 检查手机号是否已存在
      const existingPhone = await User.findOne({ where: { phoneNumber } });
      if (existingPhone) {
        return res.status(400).json({ error: '手机号已被注册' });
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(400).json({ error: '无效的手机号码格式' });
      }

      // 验证短信验证码
      if (!smsCode) {
        return res.status(400).json({ error: '请提供短信验证码' });
      }

      const isCodeValid = await verifyCode(phoneNumber, smsCode);
      if (!isCodeValid) {
        return res.status(400).json({ error: '验证码无效或已过期' });
      }

      phoneVerified = true;
    }

    // 创建新用户
    const user = await User.create({
      username,
      email,
      password, // 密码会在模型的 beforeCreate 钩子中自动加密
      fullName: fullName || null,
      phoneNumber: phoneNumber || null,
      phoneVerified
    });

    // 生成令牌
    const token = generateToken(user.id);

    // 返回用户信息（不包含密码）
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      phoneNumber: user.phoneNumber,
      phoneVerified: user.phoneVerified,
      role: user.role,
      createdAt: user.createdAt
    };

    res.status(201).json({
      message: '注册成功',
      user: userData,
      token
    });
  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({ error: '注册失败' });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password, phoneNumber, smsCode } = req.body;

    // 支持手机号+验证码登录或用户名/邮箱+密码登录
    let user;

    if (phoneNumber && smsCode) {
      // 手机号+验证码登录

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(400).json({ error: '无效的手机号码格式' });
      }

      // 验证短信验证码
      const isCodeValid = await verifyCode(phoneNumber, smsCode);
      if (!isCodeValid) {
        return res.status(401).json({ error: '验证码无效或已过期' });
      }

      // 查找用户
      user = await User.findOne({ where: { phoneNumber } });

      if (!user) {
        return res.status(401).json({ error: '该手机号未注册' });
      }

      // 更新手机号验证状态
      if (!user.phoneVerified) {
        await user.update({ phoneVerified: true });
      }
    } else if (username && password) {
      // 用户名/邮箱+密码登录

      // 查找用户（支持使用用户名或邮箱登录）
      user = await User.findOne({
        where: {
          [username.includes('@') ? 'email' : 'username']: username
        }
      });

      if (!user) {
        return res.status(401).json({ error: '用户名或密码不正确' });
      }

      // 验证密码
      const isPasswordValid = await user.validatePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json({ error: '用户名或密码不正确' });
      }
    } else {
      return res.status(400).json({ error: '请提供用户名和密码，或手机号和验证码' });
    }

    // 检查账户状态
    if (!user.isActive) {
      return res.status(401).json({ error: '账户已被停用' });
    }

    // 更新最后登录时间
    await user.update({ lastLogin: new Date() });

    // 生成令牌
    const token = generateToken(user.id);

    // 返回用户信息（不包含密码）
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      phoneNumber: user.phoneNumber,
      phoneVerified: user.phoneVerified,
      fullName: user.fullName,
      avatar: user.avatar,
      role: user.role,
      lastLogin: user.lastLogin
    };

    res.json({
      message: '登录成功',
      user: userData,
      token
    });
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({ error: '登录失败' });
  }
});

// 获取当前用户信息
router.get('/me', authenticate, async (req, res) => {
  try {
    // req.user 由 authenticate 中间件设置
    const user = req.user;

    // 返回用户信息（不包含密码）
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      phoneNumber: user.phoneNumber,
      phoneVerified: user.phoneVerified,
      fullName: user.fullName,
      avatar: user.avatar,
      bio: user.bio,
      role: user.role,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt
    };

    res.json(userData);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({ error: '获取用户信息失败' });
  }
});

// 更新用户信息
router.put('/me', authenticate, async (req, res) => {
  try {
    const { fullName, bio, avatar } = req.body;
    const user = req.user;

    // 更新用户信息
    await user.update({
      fullName: fullName !== undefined ? fullName : user.fullName,
      bio: bio !== undefined ? bio : user.bio,
      avatar: avatar !== undefined ? avatar : user.avatar
    });

    // 返回更新后的用户信息
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      avatar: user.avatar,
      bio: user.bio,
      role: user.role,
      lastLogin: user.lastLogin,
      updatedAt: user.updatedAt
    };

    res.json({
      message: '用户信息已更新',
      user: userData
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({ error: '更新用户信息失败' });
  }
});

// 修改密码
router.put('/change-password', authenticate, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const user = req.user;

    // 验证当前密码
    const isPasswordValid = await user.validatePassword(currentPassword);
    if (!isPasswordValid) {
      return res.status(401).json({ error: '当前密码不正确' });
    }

    // 更新密码
    await user.update({ password: newPassword });

    res.json({ message: '密码已更新' });
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({ error: '修改密码失败' });
  }
});

// 绑定或更新手机号
router.put('/bind-phone', authenticate, async (req, res) => {
  try {
    const { phoneNumber, smsCode } = req.body;
    const user = req.user;

    // 验证参数
    if (!phoneNumber || !smsCode) {
      return res.status(400).json({ error: '手机号和验证码不能为空' });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phoneNumber)) {
      return res.status(400).json({ error: '无效的手机号码格式' });
    }

    // 检查手机号是否已被其他用户使用
    const existingPhone = await User.findOne({
      where: {
        phoneNumber,
        id: { [Op.ne]: user.id } // 排除当前用户
      }
    });

    if (existingPhone) {
      return res.status(400).json({ error: '该手机号已被其他账号绑定' });
    }

    // 验证短信验证码
    const isCodeValid = await verifyCode(phoneNumber, smsCode);
    if (!isCodeValid) {
      return res.status(401).json({ error: '验证码无效或已过期' });
    }

    // 更新用户手机号
    await user.update({
      phoneNumber,
      phoneVerified: true
    });

    res.json({
      message: '手机号绑定成功',
      phoneNumber,
      phoneVerified: true
    });
  } catch (error) {
    console.error('绑定手机号失败:', error);
    res.status(500).json({ error: '绑定手机号失败' });
  }
});

module.exports = router;
