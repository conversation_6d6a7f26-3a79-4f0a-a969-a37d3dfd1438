const express = require('express');
const router = express.Router();
const { InterviewExperience, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const { authenticate, optionalAuth } = require('../middleware/auth');

// 获取所有面经
router.get('/', authenticate, async (req, res) => {
  try {
    const {
      company,
      position,
      result,
      difficulty,
      tag,
      search,
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'DESC'
    } = req.query;

    // 构建查询条件
    const where = {};
    if (company) where.company = company;
    if (position) where.position = position;
    if (result) where.result = result;
    if (difficulty) where.difficulty = difficulty;
    if (tag) where.tags = { [Op.contains]: [tag] };
    if (search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { content: { [Op.iLike]: `%${search}%` } },
        { company: { [Op.iLike]: `%${search}%` } },
        { position: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 分页和排序
    const offset = (page - 1) * limit;
    const orderClause = [[sort, order]];

    // 查询数据
    const { count, rows } = await InterviewExperience.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset,
      order: orderClause,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'fullName', 'avatar']
        }
      ]
    });

    // 返回结果
    res.json({
      total: count,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      data: rows
    });
  } catch (error) {
    console.error('获取面经失败:', error);
    res.status(500).json({ error: '获取面经失败' });
  }
});

// 获取单个面经
router.get('/:id', authenticate, async (req, res) => {
  try {
    const experience = await InterviewExperience.findByPk(req.params.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'fullName', 'avatar']
        }
      ]
    });

    if (!experience) {
      return res.status(404).json({ error: '面经不存在' });
    }

    // 更新浏览次数
    await experience.update({ views: experience.views + 1 });

    res.json(experience);
  } catch (error) {
    console.error('获取面经失败:', error);
    res.status(500).json({ error: '获取面经失败' });
  }
});

// 创建面经
router.post('/', authenticate, async (req, res) => {
  try {
    // 添加当前用户ID
    const experienceData = {
      ...req.body,
      authorId: req.user.id,
      author: req.user.fullName || req.user.username
    };

    const experience = await InterviewExperience.create(experienceData);

    // 获取包含用户信息的完整面经
    const fullExperience = await InterviewExperience.findByPk(experience.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'fullName', 'avatar']
        }
      ]
    });

    res.status(201).json(fullExperience);
  } catch (error) {
    console.error('创建面经失败:', error);
    res.status(500).json({ error: '创建面经失败' });
  }
});

// 更新面经
router.put('/:id', authenticate, async (req, res) => {
  try {
    const experience = await InterviewExperience.findByPk(req.params.id);
    if (!experience) {
      return res.status(404).json({ error: '面经不存在' });
    }

    // 检查权限（只有作者或管理员可以更新）
    if (experience.authorId !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: '没有权限更新此面经' });
    }

    await experience.update(req.body);

    // 获取更新后的完整面经
    const updatedExperience = await InterviewExperience.findByPk(experience.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'fullName', 'avatar']
        }
      ]
    });

    res.json(updatedExperience);
  } catch (error) {
    console.error('更新面经失败:', error);
    res.status(500).json({ error: '更新面经失败' });
  }
});

// 删除面经
router.delete('/:id', authenticate, async (req, res) => {
  try {
    const experience = await InterviewExperience.findByPk(req.params.id);
    if (!experience) {
      return res.status(404).json({ error: '面经不存在' });
    }

    // 检查权限（只有作者或管理员可以删除）
    if (experience.authorId !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ error: '没有权限删除此面经' });
    }

    await experience.destroy();
    res.status(204).end();
  } catch (error) {
    console.error('删除面经失败:', error);
    res.status(500).json({ error: '删除面经失败' });
  }
});

// 点赞面经
router.post('/:id/like', authenticate, async (req, res) => {
  try {
    const experience = await InterviewExperience.findByPk(req.params.id);
    if (!experience) {
      return res.status(404).json({ error: '面经不存在' });
    }

    await experience.update({ likes: experience.likes + 1 });
    res.json({ likes: experience.likes + 1 });
  } catch (error) {
    console.error('点赞面经失败:', error);
    res.status(500).json({ error: '点赞面经失败' });
  }
});

// 获取热门公司列表
router.get('/companies/popular', authenticate, async (req, res) => {
  try {
    const companies = await InterviewExperience.findAll({
      attributes: ['company', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['company'],
      order: [[sequelize.literal('count'), 'DESC']],
      limit: 10
    });

    res.json(companies);
  } catch (error) {
    console.error('获取热门公司失败:', error);
    res.status(500).json({ error: '获取热门公司失败' });
  }
});

// 获取热门职位列表
router.get('/positions/popular', authenticate, async (req, res) => {
  try {
    const positions = await InterviewExperience.findAll({
      attributes: ['position', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
      group: ['position'],
      order: [[sequelize.literal('count'), 'DESC']],
      limit: 10
    });

    res.json(positions);
  } catch (error) {
    console.error('获取热门职位失败:', error);
    res.status(500).json({ error: '获取热门职位失败' });
  }
});

// 获取当前用户的面经
router.get('/user/me', authenticate, async (req, res) => {
  try {
    const experiences = await InterviewExperience.findAll({
      where: {
        authorId: req.user.id
      },
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'fullName', 'avatar']
        }
      ]
    });

    res.json(experiences);
  } catch (error) {
    console.error('获取用户面经失败:', error);
    res.status(500).json({ error: '获取用户面经失败' });
  }
});

module.exports = router;
