const {
  getAsync,
  delAsync,
  setexAsync,
  incrAsync,
  expireAsync,
  ttlAsync
} = require('./redis');

// 判断是否为开发环境
const isDevelopment = process.env.NODE_ENV !== 'production';

// 从环境变量获取阿里云短信配置
const ACCESS_KEY_ID = process.env.ALIYUN_ACCESS_KEY_ID;
const ACCESS_KEY_SECRET = process.env.ALIYUN_ACCESS_KEY_SECRET;
const SMS_SIGN_NAME = process.env.ALIYUN_SMS_SIGN_NAME;
const SMS_TEMPLATE_CODE = process.env.ALIYUN_SMS_TEMPLATE_CODE;

// 内存缓存，用于存储开发环境中的验证码
const devVerificationCodes = new Map();

// 仅在生产环境中初始化阿里云短信客户端
let smsClient;
if (!isDevelopment) {
  try {
    const SMSClient = require('@alicloud/sms-sdk');
    smsClient = new SMSClient({
      accessKeyId: ACCESS_KEY_ID,
      secretAccessKey: ACCESS_KEY_SECRET
    });
  } catch (error) {
    console.error('初始化阿里云短信客户端失败:', error);
  }
}

// 从环境变量获取验证码配置
const SMS_CODE_EXPIRE = parseInt(process.env.SMS_CODE_EXPIRE || 300); // 验证码有效期（秒）
const SMS_SEND_INTERVAL = parseInt(process.env.SMS_SEND_INTERVAL || 60); // 同一手机号发送间隔（秒）
const SMS_DAY_LIMIT = parseInt(process.env.SMS_DAY_LIMIT || 10); // 同一手机号每日发送上限
const SMS_IP_MINUTE_LIMIT = parseInt(process.env.SMS_IP_MINUTE_LIMIT || 5); // 同一IP每分钟发送上限



/**
 * 生成6位数字验证码
 * @returns {string} 6位数字验证码
 */
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * 发送短信验证码
 * @param {string} phoneNumber 手机号码
 * @param {string} ip 请求IP地址
 * @returns {Promise<Object>} 发送结果
 */
async function sendVerificationCode(phoneNumber, ip) {
  try {
    // 检查IP限制
    const ipKey = `sms:ip:${ip}`;
    const ipCount = await getAsync(ipKey) || 0;

    if (parseInt(ipCount) >= SMS_IP_MINUTE_LIMIT) {
      return {
        success: false,
        message: '请求过于频繁，请稍后再试'
      };
    }

    // 检查手机号发送间隔
    const intervalKey = `sms:interval:${phoneNumber}`;
    const intervalTtl = await ttlAsync(intervalKey);

    if (intervalTtl > 0) {
      return {
        success: false,
        message: `请等待${intervalTtl}秒后再次获取验证码`
      };
    }

    // 检查手机号每日发送上限
    const dayLimitKey = `sms:day:${phoneNumber}`;
    const dayCount = await getAsync(dayLimitKey) || 0;

    if (parseInt(dayCount) >= SMS_DAY_LIMIT) {
      return {
        success: false,
        message: '今日发送验证码次数已达上限'
      };
    }

    // 生成验证码
    const code = generateVerificationCode();

    // 根据环境选择发送方式
    let sendResult = { Code: 'OK' };

    if (isDevelopment) {
      // 开发环境：模拟发送短信
      console.log('==========================================');
      console.log(`【模拟短信】发送验证码到 ${phoneNumber}: ${code}`);
      console.log('==========================================');

      // 在开发环境中，将验证码存储在内存中，以防Redis不可用
      devVerificationCodes.set(phoneNumber, code);

      // 设置5分钟后自动过期
      setTimeout(() => {
        if (devVerificationCodes.get(phoneNumber) === code) {
          devVerificationCodes.delete(phoneNumber);
        }
      }, 300000); // 5分钟
    } else {
      // 生产环境：使用阿里云短信服务
      try {
        sendResult = await smsClient.sendSMS({
          PhoneNumbers: phoneNumber,
          SignName: SMS_SIGN_NAME,
          TemplateCode: SMS_TEMPLATE_CODE,
          TemplateParam: JSON.stringify({ code })
        });
      } catch (error) {
        console.error('阿里云短信发送错误:', error);
        return {
          success: false,
          message: '短信发送失败，请稍后再试'
        };
      }
    }

    if (sendResult.Code === 'OK') {
      // 存储验证码到Redis
      const codeKey = `sms:code:${phoneNumber}`;
      await setexAsync(codeKey, SMS_CODE_EXPIRE, code);

      // 设置发送间隔
      await setexAsync(intervalKey, SMS_SEND_INTERVAL, '1');

      // 增加每日发送计数
      await incrAsync(dayLimitKey);

      // 如果是当天第一次发送，设置过期时间为当天结束
      const now = new Date();
      const endOfDay = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23, 59, 59, 999
      );
      const secondsUntilEndOfDay = Math.floor((endOfDay - now) / 1000);

      await expireAsync(dayLimitKey, secondsUntilEndOfDay);

      // 增加IP计数
      await incrAsync(ipKey);
      await expireAsync(ipKey, 60); // 1分钟过期

      return {
        success: true,
        message: '验证码已发送'
      };
    } else {
      console.error('短信发送失败:', result);
      return {
        success: false,
        message: '短信发送失败，请稍后再试'
      };
    }
  } catch (error) {
    console.error('发送验证码错误:', error);
    return {
      success: false,
      message: '发送验证码出错，请稍后再试'
    };
  }
}

/**
 * 验证短信验证码
 * @param {string} phoneNumber 手机号码
 * @param {string} code 验证码
 * @returns {Promise<boolean>} 验证结果
 */
async function verifyCode(phoneNumber, code) {
  try {
    // 开发环境特殊处理：如果验证码是"123456"，始终返回true
    if (isDevelopment && code === "123456") {
      console.log('开发环境使用默认验证码123456');
      return true;
    }

    // 先检查内存缓存
    if (isDevelopment && devVerificationCodes.has(phoneNumber)) {
      const storedCode = devVerificationCodes.get(phoneNumber);
      console.log(`从内存缓存验证码: ${phoneNumber} -> ${storedCode}`);

      // 验证后删除缓存
      if (storedCode === code) {
        devVerificationCodes.delete(phoneNumber);
        return true;
      }
    }

    // 然后检查Redis
    const codeKey = `sms:code:${phoneNumber}`;
    const storedCode = await getAsync(codeKey);

    if (!storedCode) {
      return false; // 验证码不存在或已过期
    }

    // 验证成功后删除验证码
    if (storedCode === code) {
      await delAsync(codeKey);
      return true;
    }

    return false;
  } catch (error) {
    console.error('验证码验证错误:', error);

    // 降级方案：如果Redis出错，检查内存缓存
    if (isDevelopment && devVerificationCodes.has(phoneNumber)) {
      const storedCode = devVerificationCodes.get(phoneNumber);
      if (storedCode === code) {
        devVerificationCodes.delete(phoneNumber);
        return true;
      }
    }

    return false;
  }
}

module.exports = {
  sendVerificationCode,
  verifyCode
};
