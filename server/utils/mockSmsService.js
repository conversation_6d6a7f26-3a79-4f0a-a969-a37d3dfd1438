/**
 * 模拟短信服务
 * 用于开发环境，不实际发送短信，而是将验证码打印到控制台
 */

const { 
  getAsync, 
  setexAsync, 
  incrAsync, 
  expireAsync, 
  ttlAsync 
} = require('./redis');

// 从环境变量获取验证码配置
const SMS_CODE_EXPIRE = parseInt(process.env.SMS_CODE_EXPIRE || 300); // 验证码有效期（秒）
const SMS_SEND_INTERVAL = parseInt(process.env.SMS_SEND_INTERVAL || 60); // 同一手机号发送间隔（秒）
const SMS_DAY_LIMIT = parseInt(process.env.SMS_DAY_LIMIT || 10); // 同一手机号每日发送上限
const SMS_IP_MINUTE_LIMIT = parseInt(process.env.SMS_IP_MINUTE_LIMIT || 5); // 同一IP每分钟发送上限

/**
 * 生成6位数字验证码
 * @returns {string} 6位数字验证码
 */
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * 发送短信验证码（模拟）
 * @param {string} phoneNumber 手机号码
 * @param {string} ip 请求IP地址
 * @returns {Promise<Object>} 发送结果
 */
async function sendVerificationCode(phoneNumber, ip) {
  try {
    // 检查IP限制
    const ipKey = `sms:ip:${ip}`;
    const ipCount = await getAsync(ipKey) || 0;
    
    if (parseInt(ipCount) >= SMS_IP_MINUTE_LIMIT) {
      return {
        success: false,
        message: '请求过于频繁，请稍后再试'
      };
    }
    
    // 检查手机号发送间隔
    const intervalKey = `sms:interval:${phoneNumber}`;
    const intervalTtl = await ttlAsync(intervalKey);
    
    if (intervalTtl > 0) {
      return {
        success: false,
        message: `请等待${intervalTtl}秒后再次获取验证码`
      };
    }
    
    // 检查手机号每日发送上限
    const dayLimitKey = `sms:day:${phoneNumber}`;
    const dayCount = await getAsync(dayLimitKey) || 0;
    
    if (parseInt(dayCount) >= SMS_DAY_LIMIT) {
      return {
        success: false,
        message: '今日发送验证码次数已达上限'
      };
    }
    
    // 生成验证码
    const code = generateVerificationCode();
    
    // 模拟发送短信（打印到控制台）
    console.log('==========================================');
    console.log(`【模拟短信】发送验证码到 ${phoneNumber}: ${code}`);
    console.log('==========================================');
    
    // 存储验证码到Redis
    const codeKey = `sms:code:${phoneNumber}`;
    await setexAsync(codeKey, SMS_CODE_EXPIRE, code);
    
    // 设置发送间隔
    await setexAsync(intervalKey, SMS_SEND_INTERVAL, '1');
    
    // 增加每日发送计数
    await incrAsync(dayLimitKey);
    
    // 如果是当天第一次发送，设置过期时间为当天结束
    const now = new Date();
    const endOfDay = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      23, 59, 59, 999
    );
    const secondsUntilEndOfDay = Math.floor((endOfDay - now) / 1000);
    
    await expireAsync(dayLimitKey, secondsUntilEndOfDay);
    
    // 增加IP计数
    await incrAsync(ipKey);
    await expireAsync(ipKey, 60); // 1分钟过期
    
    return {
      success: true,
      message: '验证码已发送'
    };
  } catch (error) {
    console.error('发送验证码错误:', error);
    return {
      success: false,
      message: '发送验证码出错，请稍后再试'
    };
  }
}

/**
 * 验证短信验证码
 * @param {string} phoneNumber 手机号码
 * @param {string} code 验证码
 * @returns {Promise<boolean>} 验证结果
 */
async function verifyCode(phoneNumber, code) {
  try {
    const codeKey = `sms:code:${phoneNumber}`;
    const storedCode = await getAsync(codeKey);
    
    if (!storedCode) {
      return false; // 验证码不存在或已过期
    }
    
    return storedCode === code;
  } catch (error) {
    console.error('验证码验证错误:', error);
    return false;
  }
}

module.exports = {
  sendVerificationCode,
  verifyCode
};
