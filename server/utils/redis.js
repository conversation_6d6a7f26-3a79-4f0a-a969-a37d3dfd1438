const redis = require('redis');

// 从环境变量获取Redis配置
const REDIS_HOST = process.env.REDIS_HOST || 'localhost';
const REDIS_PORT = process.env.REDIS_PORT || 6379;
const REDIS_PASSWORD = process.env.REDIS_PASSWORD || '';
const REDIS_DB = process.env.REDIS_DB || 0;

// 标记Redis是否可用
let redisAvailable = false;

// 创建Redis客户端
const redisClient = redis.createClient({
  url: `redis://${REDIS_HOST}:${REDIS_PORT}`,
  password: REDIS_PASSWORD || undefined,
  database: parseInt(REDIS_DB)
});

// 连接Redis
(async () => {
  try {
    await redisClient.connect();
    redisAvailable = true;
    console.log('Redis客户端已连接');
  } catch (err) {
    redisAvailable = false;
    console.error('Redis连接错误:', err);
    console.log('应用将在没有Redis的情况下运行，某些功能可能不可用');
  }
})();

// 监听错误事件
redisClient.on('error', (err) => {
  console.error('Redis错误:', err);
  redisAvailable = false;
});

// 监听重连事件
redisClient.on('reconnecting', () => {
  console.log('Redis客户端正在重新连接...');
});

// 监听结束事件
redisClient.on('end', () => {
  console.log('Redis客户端已断开连接');
  redisAvailable = false;
});

// 监听连接事件
redisClient.on('connect', () => {
  console.log('Redis客户端已重新连接');
  redisAvailable = true;
});

// 内存缓存，用于Redis不可用时的降级方案
const memoryCache = new Map();
const memoryCacheTTL = new Map();

// 定期清理过期的内存缓存项
setInterval(() => {
  const now = Date.now();
  for (const [key, expiry] of memoryCacheTTL.entries()) {
    if (now > expiry) {
      memoryCache.delete(key);
      memoryCacheTTL.delete(key);
    }
  }
}, 60000); // 每分钟清理一次

// 封装Redis操作函数，带有降级机制
const getAsync = async (key) => {
  try {
    if (redisAvailable) {
      return await redisClient.get(key);
    } else {
      return memoryCache.get(key) || null;
    }
  } catch (error) {
    console.error(`Redis getAsync error for key ${key}:`, error);
    return memoryCache.get(key) || null;
  }
};

const setAsync = async (key, value) => {
  try {
    if (redisAvailable) {
      return await redisClient.set(key, value);
    } else {
      memoryCache.set(key, value);
      return 'OK';
    }
  } catch (error) {
    console.error(`Redis setAsync error for key ${key}:`, error);
    memoryCache.set(key, value);
    return 'OK';
  }
};

const setexAsync = async (key, seconds, value) => {
  try {
    if (redisAvailable) {
      return await redisClient.setEx(key, seconds, value);
    } else {
      memoryCache.set(key, value);
      memoryCacheTTL.set(key, Date.now() + (seconds * 1000));
      return 'OK';
    }
  } catch (error) {
    console.error(`Redis setexAsync error for key ${key}:`, error);
    memoryCache.set(key, value);
    memoryCacheTTL.set(key, Date.now() + (seconds * 1000));
    return 'OK';
  }
};

const delAsync = async (key) => {
  try {
    if (redisAvailable) {
      return await redisClient.del(key);
    } else {
      const existed = memoryCache.has(key);
      memoryCache.delete(key);
      memoryCacheTTL.delete(key);
      return existed ? 1 : 0;
    }
  } catch (error) {
    console.error(`Redis delAsync error for key ${key}:`, error);
    const existed = memoryCache.has(key);
    memoryCache.delete(key);
    memoryCacheTTL.delete(key);
    return existed ? 1 : 0;
  }
};

const incrAsync = async (key) => {
  try {
    if (redisAvailable) {
      return await redisClient.incr(key);
    } else {
      const currentValue = parseInt(memoryCache.get(key) || '0', 10);
      const newValue = currentValue + 1;
      memoryCache.set(key, newValue.toString());
      return newValue;
    }
  } catch (error) {
    console.error(`Redis incrAsync error for key ${key}:`, error);
    const currentValue = parseInt(memoryCache.get(key) || '0', 10);
    const newValue = currentValue + 1;
    memoryCache.set(key, newValue.toString());
    return newValue;
  }
};

const expireAsync = async (key, seconds) => {
  try {
    if (redisAvailable) {
      return await redisClient.expire(key, seconds);
    } else {
      if (memoryCache.has(key)) {
        memoryCacheTTL.set(key, Date.now() + (seconds * 1000));
        return 1;
      }
      return 0;
    }
  } catch (error) {
    console.error(`Redis expireAsync error for key ${key}:`, error);
    if (memoryCache.has(key)) {
      memoryCacheTTL.set(key, Date.now() + (seconds * 1000));
      return 1;
    }
    return 0;
  }
};

const ttlAsync = async (key) => {
  try {
    if (redisAvailable) {
      return await redisClient.ttl(key);
    } else {
      const expiry = memoryCacheTTL.get(key);
      if (!expiry) return -2; // 键不存在
      const ttl = Math.ceil((expiry - Date.now()) / 1000);
      return ttl > 0 ? ttl : -1; // 如果已过期，返回-1
    }
  } catch (error) {
    console.error(`Redis ttlAsync error for key ${key}:`, error);
    const expiry = memoryCacheTTL.get(key);
    if (!expiry) return -2; // 键不存在
    const ttl = Math.ceil((expiry - Date.now()) / 1000);
    return ttl > 0 ? ttl : -1; // 如果已过期，返回-1
  }
};

module.exports = {
  redisClient,
  getAsync,
  setAsync,
  setexAsync,
  delAsync,
  incrAsync,
  expireAsync,
  ttlAsync,
  redisAvailable
};
