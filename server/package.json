{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@alicloud/sms-sdk": "^1.1.6", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.0", "multer": "^1.4.5-lts.2", "openai": "^4.96.0", "pdf-parse": "^1.1.1", "pg": "^8.15.6", "pg-hstore": "^2.3.4", "redis": "^5.0.1", "sequelize": "^6.37.7"}}